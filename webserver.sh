#!/usr/bin/env bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help         显示此帮助信息
    -c, --config FILE  配置文件 (覆盖 CONFIG_FILE)
    -t, --test         测试模式，不使用tmux

示例:
    $0 -d /path/to/data -c config/test.json

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 设置webserver专用的默认配置文件
CONFIG_FILE="user_data/config_webserver.json"
TEST=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -c | --config)
        CONFIG_FILE="$2"
        shift 2
        ;;
    -t | --test)
        TEST=true
        shift
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 构建freqtrade命令
freqtrade_cmd=(
    freqtrade webserver
    --data-dir "${DATA_DIR}"
    --config "${CONFIG_FILE}"
)

if [[ "$TEST" == "true" ]]; then
    "${freqtrade_cmd[@]}"
else
    session_name="webserver"

    if tmux ls | grep "$session_name:" >/dev/null; then
        tmux -u attach -t "$session_name"
    else
        tmux -u new-session -s "$session_name" -- \
            "${freqtrade_cmd[@]}"
    fi
fi
