import pandas as pd
import os
from pathlib import Path

def check_mark_price_data(data_dir="user_data/data", exchange="binance"):
    """
    检查标记价格数据文件内容
    """
    futures_dir = Path(data_dir) / exchange / "futures"
    
    # 查找8小时标记价格文件
    mark_files = list(futures_dir.glob("*-8h-mark.feather"))
    
    if not mark_files:
        print("未找到8小时标记价格文件")
        return
    
    # 检查第一个文件
    mark_file = mark_files[0]
    print(f"检查文件: {mark_file}")
    
    try:
        # 读取feather文件
        df = pd.read_feather(mark_file)
        
        print(f"\n数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print(f"数据类型:\n{df.dtypes}")
        print(f"\n前5行数据:")
        print(df.head())
        print(f"\n后5行数据:")
        print(df.tail())
        print(f"\n数据统计:")
        print(df.describe())
        
        # 检查时间范围
        if 'date' in df.columns:
            print(f"\n时间范围:")
            print(f"开始时间: {df['date'].min()}")
            print(f"结束时间: {df['date'].max()}")
            print(f"数据点数: {len(df)}")
            
        # 检查价格范围
        price_cols = ['open', 'high', 'low', 'close']
        available_price_cols = [col for col in price_cols if col in df.columns]
        if available_price_cols:
            print(f"\n价格统计:")
            for col in available_price_cols:
                print(f"{col}: 最小={df[col].min():.4f}, 最大={df[col].max():.4f}, 平均={df[col].mean():.4f}")
                
    except Exception as e:
        print(f"读取文件出错: {e}")

if __name__ == "__main__":
    check_mark_price_data()