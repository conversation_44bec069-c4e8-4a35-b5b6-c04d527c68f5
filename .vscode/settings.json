{"augment.advanced": {"mcpServers": [{"name": "context7", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, {"name": "playwright", "command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, {"name": "sequentialthinking", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, {"name": "fetch", "command": "npx", "args": ["-y", "@tokenizin/mcp-npx-fetch@latest"]}, {"name": "memory", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory@latest"], "env": {"MEMORY_FILE_PATH": "${input:memory_file_path}"}}]}}