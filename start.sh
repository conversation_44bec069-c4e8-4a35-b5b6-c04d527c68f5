#!/usr/bin/env bash
# shellcheck disable=SC1091

# 设置日志目录和配置文件路径
LOG_DIR="user_data/logs"
CONFIG_FILE="user_data/config.json"
DB_URL="sqlite:///user_data/tradesv3.sqlite"

# 删除旧日志文件
find "$LOG_DIR" -type f \( -size -100k -o -mtime +4 \) | sort -r | tail -n +6 | while read -r file; do
    rm "$file"
    echo "Deleted: $file"
done
# 生成日志文件名
LOG_FILE="$LOG_DIR/$(date +"%Y%m%d-%H%M%S").log"

# 激活虚拟环境
source /Users/<USER>/freqAI/freqtrade/.venv/bin/activate
export NUMEXPR_MAX_THREADS=32
export HTTPS_PROXY="http://localhost:3213"
export NO_PROXY="127.0.0.1,**********/12,***********/16,::1,localhost,.local,.docker.internal"

# 执行 freqtrade 主程序
freqtrade trade \
    --config "$CONFIG_FILE" \
    --logfile "$LOG_FILE" \
    --db-url "$DB_URL"
