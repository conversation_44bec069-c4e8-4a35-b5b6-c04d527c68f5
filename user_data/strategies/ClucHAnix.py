from datetime import datetime

import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import (
    DecimalParameter,
    IntParameter,
    merge_informative_pair,
    stoploss_from_open,
)
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame, Series


def bollinger_bands(stock_price, window_size, num_of_std):
    """
    自定义布林带计算函数

    Args:
        stock_price: 价格序列
        window_size: 计算窗口大小
        num_of_std: 标准差倍数

    Returns:
        tuple: (中轨, 下轨) - 返回布林带中轨和下轨
    """
    rolling_mean = stock_price.rolling(window=window_size).mean()
    rolling_std = stock_price.rolling(window=window_size).std()
    lower_band = rolling_mean - (rolling_std * num_of_std)
    return np.nan_to_num(rolling_mean), np.nan_to_num(lower_band)


def ha_typical_price(bars):
    """
    计算Heiken Ashi典型价格

    Args:
        bars: 包含ha_high, ha_low, ha_close的DataFrame

    Returns:
        Series: Heiken Ashi典型价格 = (最高价 + 最低价 + 收盘价) / 3
    """
    res = (bars["ha_high"] + bars["ha_low"] + bars["ha_close"]) / 3.0
    return Series(index=bars.index, data=res)


class ClucHAnix(IStrategy):
    """
    ClucHAnix策略 - 基于Heiken Ashi和Fisher Transform的复合策略

    策略特点：
    - 使用Heiken Ashi蜡烛图平滑价格数据
    - 结合Fisher Transform指标识别趋势转折点
    - 布林带中线作为趋势过滤器
    - 自定义动态止损机制
    - 支持超参数优化

    技术指标组合：
    - Heiken Ashi: 平滑的蜡烛图，减少噪音
    - Fisher Transform: 将价格数据转换为近似正态分布
    - 布林带: 识别价格相对位置和波动性
    - EMA: 趋势方向判断

    注意：可以通过超参数优化输出覆盖默认参数
    """

    # Sell hyperspace params:
    sell_params = {"sell_fisher": 0.38414, "sell_bbmiddle_close": 1.07634}

    # ROI table:
    minimal_roi = {"0": 100}

    # Stoploss:
    stoploss = -0.99  # use custom stoploss

    # Trailing stop:
    trailing_stop = False
    trailing_stop_positive = 0.001
    trailing_stop_positive_offset = 0.012
    trailing_only_offset_is_reached = False

    """
    END HYPEROPT
    """

    timeframe = "1m"

    # Custom stoploss
    use_custom_stoploss = True

    process_only_new_candles = True
    startup_candle_count = 168

    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_limit_ratio": 0.99,
    }

    # buy params
    buy_op = True
    bbdelta_close = DecimalParameter(
        0.0005, 0.02, default=0.02, decimals=4, space="buy", optimize=buy_op
    )
    bbdelta_tail = DecimalParameter(0.7, 1.0, default=0.7, decimals=4, space="buy", optimize=buy_op)
    close_bblower = DecimalParameter(
        0.0005, 0.02, default=0.02, decimals=4, space="buy", optimize=buy_op
    )
    closedelta_close = DecimalParameter(
        0.0005, 0.02, default=0.02, decimals=4, space="buy", optimize=buy_op
    )
    rocr_1h = DecimalParameter(0.5, 1.0, default=0.5, decimals=4, space="buy", optimize=buy_op)

    leverage_optimize = True
    leverage_num = IntParameter(low=1, high=10, default=1, space="buy", optimize=leverage_optimize)

    # sell params
    sell_op = False
    sell_fisher = DecimalParameter(
        0.1, 0.5, default=0.5, decimals=4, space="sell", optimize=sell_op
    )
    sell_bbmiddle_close = DecimalParameter(
        0.97, 1.1, default=1.1, decimals=4, space="sell", optimize=sell_op
    )

    # trailing stoploss
    trailing_optimize = True
    pHSL = DecimalParameter(
        -0.990, -0.040, default=-0.08, decimals=3, space="sell", optimize=trailing_optimize
    )
    pPF_1 = DecimalParameter(
        0.008, 0.100, default=0.016, decimals=3, space="sell", optimize=trailing_optimize
    )
    pSL_1 = DecimalParameter(
        0.008, 0.100, default=0.011, decimals=3, space="sell", optimize=trailing_optimize
    )
    pPF_2 = DecimalParameter(
        0.040, 0.200, default=0.080, decimals=3, space="sell", optimize=trailing_optimize
    )
    pSL_2 = DecimalParameter(
        0.040, 0.200, default=0.040, decimals=3, space="sell", optimize=trailing_optimize
    )

    def informative_pairs(self):
        pairs = self.dp.current_whitelist()
        informative_pairs = [(pair, "1h") for pair in pairs]
        return informative_pairs

    def custom_stoploss(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ) -> float:
        """
        自定义动态止损函数

        实现分阶段的动态止损机制：
        1. 当盈利低于PF_1时，使用硬止损HSL
        2. 当盈利在PF_1和PF_2之间时，线性插值计算止损
        3. 当盈利高于PF_2时，止损随盈利线性增长

        这种机制可以在保护利润的同时，给予交易更多的盈利空间

        Args:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利百分比

        Returns:
            float: 止损百分比，1表示立即止损
        """
        # 获取止损参数值
        HSL = self.pHSL.value  # 硬止损水平
        PF_1 = self.pPF_1.value  # 第一个盈利阈值
        SL_1 = self.pSL_1.value  # 第一阶段止损水平
        PF_2 = self.pPF_2.value  # 第二个盈利阈值
        SL_2 = self.pSL_2.value  # 第二阶段止损水平

        # 分阶段计算止损水平：
        # - 盈利在PF_1和PF_2之间时，止损水平线性插值
        # - 盈利超过PF_2时，止损水平随盈利线性增长
        # - 盈利低于PF_1时，使用硬止损HSL

        if current_profit > PF_2:
            # 高盈利阶段：止损随盈利增长
            sl_profit = SL_2 + (current_profit - PF_2)
        elif current_profit > PF_1:
            # 中等盈利阶段：线性插值计算止损
            sl_profit = SL_1 + ((current_profit - PF_1) * (SL_2 - SL_1) / (PF_2 - PF_1))
        else:
            # 低盈利或亏损阶段：使用硬止损
            sl_profit = HSL

        # 检查止损计算是否有效，避免数学错误
        if self.can_short:
            if (-1 + ((1 - sl_profit) / (1 - current_profit))) <= 0:
                return 1
        else:
            if (1 - ((1 + sl_profit) / (1 + current_profit))) <= 0:
                return 1

        # 返回基于开仓价格的止损水平
        return stoploss_from_open(sl_profit, current_profit, is_short=trade.is_short)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的各种技术指标：
        1. Heiken Ashi蜡烛图：平滑价格数据，减少噪音
        2. 布林带：基于HA典型价格计算，识别价格相对位置
        3. Fisher Transform：将RSI转换为近似正态分布
        4. EMA：趋势方向判断
        5. 1小时时间框架的辅助指标

        Args:
            dataframe: OHLCV价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了技术指标的数据框
        """
        # Heiken Ashi蜡烛图 - 平滑价格数据，减少市场噪音
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe["ha_open"] = heikinashi["open"]  # HA开盘价
        dataframe["ha_close"] = heikinashi["close"]  # HA收盘价
        dataframe["ha_high"] = heikinashi["high"]  # HA最高价
        dataframe["ha_low"] = heikinashi["low"]  # HA最低价

        # 基于HA典型价格的布林带设置
        mid, lower = bollinger_bands(ha_typical_price(dataframe), window_size=40, num_of_std=2)
        dataframe["lower"] = lower  # 布林带下轨
        dataframe["mid"] = mid  # 布林带中轨

        # 布林带相关衍生指标
        dataframe["bbdelta"] = (mid - dataframe["lower"]).abs()  # 布林带中轨与下轨的差值
        dataframe["closedelta"] = (
            dataframe["ha_close"] - dataframe["ha_close"].shift()
        ).abs()  # HA收盘价变化幅度
        dataframe["tail"] = (dataframe["ha_close"] - dataframe["ha_low"]).abs()  # HA下影线长度

        # 布林带别名（兼容性）
        dataframe["bb_lowerband"] = dataframe["lower"]  # 布林带下轨别名
        dataframe["bb_middleband"] = dataframe["mid"]  # 布林带中轨别名

        # 移动平均线系统
        dataframe["ema_fast"] = ta.EMA(dataframe["ha_close"], timeperiod=3)  # 3周期快速EMA
        dataframe["ema_slow"] = ta.EMA(dataframe["ha_close"], timeperiod=50)  # 50周期慢速EMA
        dataframe["volume_mean_slow"] = (
            dataframe["volume"].rolling(window=30).mean()
        )  # 30周期成交量均值
        dataframe["rocr"] = ta.ROCR(dataframe["ha_close"], timeperiod=28)  # 28周期变化率

        # Fisher Transform指标 - 将RSI转换为近似正态分布
        rsi = ta.RSI(dataframe)  # 计算RSI
        dataframe["rsi"] = rsi  # 保存原始RSI
        rsi = 0.1 * (rsi - 50)  # 将RSI标准化到[-5, 5]区间
        dataframe["fisher"] = (np.exp(2 * rsi) - 1) / (np.exp(2 * rsi) + 1)  # Fisher Transform公式

        # 1小时时间框架的辅助指标
        inf_tf = "1h"  # 辅助时间框架

        # 获取1小时时间框架数据
        informative = self.dp.get_pair_dataframe(pair=metadata["pair"], timeframe=inf_tf)

        # 计算1小时HA数据
        inf_heikinashi = qtpylib.heikinashi(informative)

        informative["ha_close"] = inf_heikinashi["close"]  # 1小时HA收盘价
        informative["rocr"] = ta.ROCR(
            informative["ha_close"], timeperiod=168
        )  # 1小时168周期变化率（约7天）

        # 合并1小时数据到主时间框架
        dataframe = merge_informative_pair(
            dataframe, informative, self.timeframe, inf_tf, ffill=True
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场趋势信号

        实现两种买入策略的组合：
        1. 主要策略：基于布林带突破和HA蜡烛图形态的精确入场
        2. 辅助策略：基于EMA和布林带的简单超卖入场

        两种策略都需要满足1小时时间框架的变化率条件作为大趋势过滤

        Args:
            dataframe: 包含技术指标的价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        dataframe.loc[
            # 大趋势过滤：1小时变化率必须大于阈值（确保处于上升趋势）
            (dataframe["rocr_1h"].gt(self.rocr_1h.value))
            & (
                # 主要买入策略：基于布林带突破和HA蜡烛图形态
                (
                    (dataframe["lower"].shift().gt(0))  # 布林带下轨有效（前一期大于0）
                    & (
                        dataframe["bbdelta"].gt(dataframe["ha_close"] * self.bbdelta_close.value)
                    )  # 布林带宽度相对HA收盘价足够大
                    & (
                        dataframe["closedelta"].gt(
                            dataframe["ha_close"] * self.closedelta_close.value
                        )
                    )  # HA收盘价变化幅度相对收盘价足够大
                    & (
                        dataframe["tail"].lt(dataframe["bbdelta"] * self.bbdelta_tail.value)
                    )  # 下影线长度相对布林带宽度较小
                    & (
                        dataframe["ha_close"].lt(dataframe["lower"].shift())
                    )  # HA收盘价低于前一期布林带下轨（突破下轨）
                    & (
                        dataframe["ha_close"].le(dataframe["ha_close"].shift())
                    )  # HA收盘价不高于前一期（价格下降或持平）
                )
                # 或者 辅助买入策略：基于EMA和布林带的简单超卖
                | (
                    (
                        dataframe["ha_close"] < dataframe["ema_slow"]
                    )  # HA收盘价低于50周期慢速EMA（趋势下方）
                    & (
                        dataframe["ha_close"] < self.close_bblower.value * dataframe["bb_lowerband"]
                    )  # HA收盘价低于布林带下轨的一定比例（深度超卖）
                )
            ),
            "enter_long",  # 设置多头入场信号
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场趋势信号

        实现基于Fisher Transform和HA蜡烛图形态的卖出策略：
        - Fisher Transform超买信号
        - HA最高价连续下降（动能减弱）
        - HA收盘价下降（价格走弱）
        - 快速EMA高于HA收盘价（短期趋势转向）
        - HA收盘价相对布林带中轨位置（价格相对强度）

        Args:
            dataframe: 包含技术指标的价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了出场信号的数据框
        """
        dataframe.loc[
            (dataframe["fisher"] > self.sell_fisher.value)  # Fisher Transform超买（大于阈值）
            & (dataframe["ha_high"].le(dataframe["ha_high"].shift(1)))  # HA最高价不高于前一期
            & (
                dataframe["ha_high"].shift(1).le(dataframe["ha_high"].shift(2))
            )  # 前一期HA最高价不高于前两期（连续下降）
            & (
                dataframe["ha_close"].le(dataframe["ha_close"].shift(1))
            )  # HA收盘价不高于前一期（价格走弱）
            & (
                dataframe["ema_fast"] > dataframe["ha_close"]
            )  # 3周期快速EMA高于HA收盘价（短期趋势转向）
            & (
                (dataframe["ha_close"] * self.sell_bbmiddle_close.value)
                > dataframe["bb_middleband"]
            )  # HA收盘价的一定倍数高于布林带中轨（价格相对强度足够）
            & (dataframe["volume"] > 0),  # 成交量大于0（确保有交易活动）
            "exit_long",  # 设置多头出场信号
        ] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        优先从配置文件中读取杠杆设置，如果配置文件中没有设置则使用策略内部的超参数

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 优先从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", None)

        if config_leverage is not None:
            # 使用配置文件中的杠杆设置
            leverage = config_leverage
        else:
            # 如果配置文件中没有设置，使用策略内部的超参数
            leverage = self.leverage_num.value

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(leverage, max_leverage))

        return leverage
