import warnings
from datetime import datetime, timedelta
from functools import reduce

import pandas_ta as pta
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

# 忽略运行时警告
warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1EN(IStrategy):
    """
    E0V1EN策略 - 基于RSI和24小时价格变化的多条件交易策略

    策略特点：
    - 使用RSI指标识别超买超卖状态
    - 结合CTI指标进行趋势确认
    - 24小时价格变化作为市场情绪过滤器
    - 自定义退出逻辑，包括时间止损和指标止损
    - 支持追踪止损

    技术指标组合：
    - RSI: 相对强弱指标，多个周期组合
    - CTI: 相关趋势指标，衡量价格动量
    - SMA: 简单移动平均线，趋势过滤
    - 24小时价格变化: 市场情绪指标
    - Stochastic Fast K: 随机指标，用于退出
    - CCI: 商品通道指标，用于退出
    """

    # 最小ROI设置为100%，主要通过自定义退出控制
    minimal_roi = {"0": 1}

    # 5分钟时间框架，适合短线交易
    timeframe = "5m"
    # 仅在新K线时处理，提高效率
    process_only_new_candles = True
    # 启动时需要20根K线用于指标计算
    startup_candle_count = 20

    # 订单类型配置 - 全部使用市价单确保快速成交
    order_types = {
        "entry": "market",  # 入场订单：市价单
        "exit": "market",  # 出场订单：市价单
        "emergency_exit": "market",  # 紧急出场：市价单
        "force_entry": "market",  # 强制入场：市价单
        "force_exit": "market",  # 强制出场：市价单
        "stoploss": "market",  # 止损订单：市价单
        "stoploss_on_exchange": False,  # 不在交易所设置止损
        "stoploss_on_exchange_interval": 60,  # 交易所止损检查间隔
        "stoploss_on_exchange_market_ratio": 0.99,  # 交易所止损市价比例
    }

    # 固定止损25%
    stoploss = -0.25
    # 启用追踪止损
    trailing_stop = True
    trailing_stop_positive = 0.002  # 盈利0.2%后开始追踪止损
    trailing_stop_positive_offset = 0.03  # 追踪止损偏移3%
    trailing_only_offset_is_reached = True  # 只有达到偏移后才启用追踪

    # =============================================================================
    # 买入参数配置 - RSI和CTI相关参数
    # =============================================================================
    is_optimize_32 = False  # 是否优化这组参数
    buy_rsi_fast_32 = IntParameter(
        20, 70, default=40, space="buy", optimize=is_optimize_32
    )  # 快速RSI阈值
    buy_rsi_32 = IntParameter(
        15, 50, default=42, space="buy", optimize=is_optimize_32
    )  # 标准RSI阈值
    buy_sma15_32 = DecimalParameter(
        0.900, 1, default=0.973, decimals=3, space="buy", optimize=is_optimize_32
    )  # SMA15倍数
    buy_cti_32 = DecimalParameter(
        -1, 1, default=0.69, decimals=2, space="buy", optimize=is_optimize_32
    )  # CTI阈值

    # =============================================================================
    # 24小时价格变化过滤参数 - 用于市场情绪判断
    # =============================================================================
    # 第一组24小时变化参数（用于buy_new策略）
    buy_24h_min_pct = DecimalParameter(
        -30.0, 0.0, default=-15.0, decimals=1, space="buy", optimize=True
    )  # 24小时最小变化百分比
    buy_24h_max_pct = DecimalParameter(
        0.0, 200.0, default=50.0, decimals=1, space="buy", optimize=True
    )  # 24小时最大变化百分比

    # 第二组24小时变化参数（用于buy_1策略）
    buy_24h_min_pct1 = DecimalParameter(
        -30.0, 0.0, default=-15.0, decimals=1, space="buy", optimize=True
    )  # 24小时最小变化百分比
    buy_24h_max_pct1 = DecimalParameter(
        0.0, 200.0, default=50.0, decimals=1, space="buy", optimize=True
    )  # 24小时最大变化百分比

    # =============================================================================
    # 卖出参数配置
    # =============================================================================
    sell_fastx = IntParameter(
        50, 100, default=84, space="sell", optimize=True
    )  # Stochastic Fast K卖出阈值

    @property
    def protections(self):
        """
        保护机制配置

        设置冷却期保护，防止频繁交易：
        - 在交易结束后96个K线周期内不允许新的交易
        - 5分钟时间框架下相当于8小时的冷却期

        Returns:
            list: 保护机制配置列表
        """
        return [{"method": "CooldownPeriod", "stop_duration_candles": 96}]

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的各种技术指标：
        1. RSI系列：多个周期的RSI指标，用于识别超买超卖
        2. CTI：相关趋势指标，衡量价格动量
        3. SMA：简单移动平均线，用于趋势过滤
        4. 24小时价格变化：市场情绪指标
        5. 退出指标：Stochastic Fast K和CCI

        Args:
            dataframe: OHLCV价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了技术指标的数据框
        """
        # 买入信号相关指标
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)  # 15周期简单移动平均线
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)  # 20周期相关趋势指标
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)  # 14周期标准RSI
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)  # 4周期快速RSI
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)  # 20周期慢速RSI

        # 24小时价格变化百分比（288个5分钟K线 = 24小时）
        dataframe["24h_change_pct"] = dataframe["close"].pct_change(periods=288) * 100

        # 退出信号相关指标
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)  # 5周期随机指标
        dataframe["fastk"] = stoch_fast["fastk"]  # 随机指标快线K值
        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)  # 20周期商品通道指标

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场趋势信号

        实现两种买入策略：
        1. buy_1: 基于可优化参数的RSI策略
        2. buy_new: 基于固定参数的RSI策略

        两种策略的共同特点：
        - RSI慢线下降（动能减弱）
        - 快速RSI超卖
        - 标准RSI不过度超卖
        - 价格低于SMA15（趋势下方）
        - CTI指标确认
        - 24小时价格变化在合理范围内

        Args:
            dataframe: 包含技术指标的价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # 买入策略1：基于可优化参数的RSI策略
        buy_1 = (
            (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))  # 20周期RSI下降（动能减弱）
            & (dataframe["rsi_fast"] < self.buy_rsi_fast_32.value)  # 4周期快速RSI超卖
            & (dataframe["rsi"] > self.buy_rsi_32.value)  # 14周期标准RSI不过度超卖
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma15_32.value)  # 价格低于SMA15
            & (dataframe["cti"] < self.buy_cti_32.value)  # CTI指标确认趋势
            & (dataframe["24h_change_pct"] > self.buy_24h_min_pct1.value)  # 24小时变化不过度负面
            & (dataframe["24h_change_pct"] < self.buy_24h_max_pct1.value)  # 24小时变化不过度正面
        )

        # 买入策略2：基于固定参数的RSI策略（更保守）
        buy_new = (
            (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))  # 20周期RSI下降
            & (dataframe["rsi_fast"] < 34)  # 4周期快速RSI超卖（固定阈值）
            & (dataframe["rsi"] > 28)  # 14周期标准RSI不过度超卖（固定阈值）
            & (dataframe["close"] < dataframe["sma_15"] * 0.96)  # 价格低于SMA15的96%（更严格）
            & (dataframe["cti"] < self.buy_cti_32.value)  # CTI指标确认
            & (dataframe["24h_change_pct"] > self.buy_24h_min_pct.value)  # 24小时变化范围控制
            & (dataframe["24h_change_pct"] < self.buy_24h_max_pct.value)  # 24小时变化范围控制
        )

        # 添加买入条件到条件列表
        conditions.append(buy_1)
        dataframe.loc[buy_1, "enter_tag"] += "buy_1"

        conditions.append(buy_new)
        dataframe.loc[buy_new, "enter_tag"] += "buy_new"

        # 如果有任何买入条件满足，设置入场信号
        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1
        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        自定义退出逻辑

        实现多种退出条件：
        1. 盈利退出：针对buy_new策略，当Stochastic Fast K超买时退出
        2. 止损退出：当CCI超买且亏损不大时退出
        3. 时间止损：基于持仓时间的分阶段止损

        退出优先级：
        - 盈利退出 > 指标止损 > 时间止损

        Args:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利百分比

        Returns:
            str or None: 退出标签，None表示不退出
        """
        # 获取当前K线数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # 盈利退出：仅针对buy_new策略，当有盈利且Stochastic Fast K超买时退出
        if current_profit > 0 and "buy_new" == str(trade.enter_tag):
            if current_candle["fastk"] > self.sell_fastx.value:
                return "fastk_profit_sell"

        # 指标止损：当亏损不超过3%且CCI超买时退出（防止进一步亏损）
        if current_profit > -0.03:
            if current_candle["cci"] > 80:
                return "cci_loss_sell"

        # 时间止损1：持仓7小时后，如果亏损不超过5%则退出
        if current_time - timedelta(hours=7) > trade.open_date_utc:
            if current_profit >= -0.05:
                return "time_loss_sell_7_5"

        # 时间止损2：持仓10小时后，如果亏损不超过10%则退出
        if current_time - timedelta(hours=10) > trade.open_date_utc:
            if current_profit >= -0.1:
                return "time_loss_sell_10_10"

        # 不满足任何退出条件，继续持仓
        return None

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充出场趋势信号

        由于使用自定义退出逻辑，这里不设置任何基于指标的退出信号
        所有退出决策都在custom_exit函数中处理

        Args:
            dataframe: 包含技术指标的价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 不包含退出信号的数据框
        """
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, "long_out")
        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        从配置文件中读取杠杆设置，如果配置文件中没有设置则使用默认值1.0

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", 1.0)

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(config_leverage, max_leverage))

        return leverage
