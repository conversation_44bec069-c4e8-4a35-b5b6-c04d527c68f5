from datetime import datetime
from functools import reduce

import numpy  # noqa
import talib.abstract as ta
from freqtrade.strategy import IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame


class BinHV27F(IStrategy):
    """
    BinHV27F策略 - 基于ADX和EMA-RSI的多条件交易策略

    策略来源：由Slack用户BinH赞助开发

    策略特点：
    - 使用ADX（平均趋向指数）判断趋势强度
    - 结合EMA-RSI指标识别超买超卖状态
    - 多组参数组合，提供4种不同的买入条件
    - 3种不同的卖出条件
    - 适合5分钟时间框架的短线交易

    技术指标组合：
    - ADX: 趋势强度指标，值越高表示趋势越强
    - EMA-RSI: 指数移动平均RSI，更平滑的超买超卖信号
    - RSI: 相对强弱指标，判断价格动量
    """

    # 最小ROI设置为100%，实际通过止损和卖出信号控制
    minimal_roi = {"0": 100}

    # 买入参数配置 - 4组不同的ADX和EMA-RSI组合
    buy_params = {
        "buy_adx1": 25,  # 第1组：ADX趋势强度阈值
        "buy_emarsi1": 20,  # 第1组：EMA-RSI超卖阈值
        "buy_adx2": 30,  # 第2组：ADX趋势强度阈值
        "buy_emarsi2": 20,  # 第2组：EMA-RSI超卖阈值
        "buy_adx3": 35,  # 第3组：ADX趋势强度阈值
        "buy_emarsi3": 20,  # 第3组：EMA-RSI超卖阈值
        "buy_adx4": 30,  # 第4组：ADX趋势强度阈值
        "buy_emarsi4": 25,  # 第4组：EMA-RSI超卖阈值
    }

    # 卖出参数配置 - 3种不同的卖出条件
    sell_params = {
        "emarsi1": 75,  # 第1种：EMA-RSI超买阈值
        "adx2": 30,  # 第2种：ADX趋势减弱阈值
        "emarsi2": 80,  # 第2种：EMA-RSI超买阈值
        "emarsi3": 75,  # 第3种：EMA-RSI超买阈值
    }

    # 固定止损25%
    stoploss = -0.25
    # 5分钟时间框架
    timeframe = "5m"

    # 仅在新K线时处理，提高效率
    process_only_new_candles = True
    # 启动时需要240根K线用于指标计算
    startup_candle_count = 240

    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_limit_ratio": 0.99,
    }

    # buy params
    buy_adx1 = IntParameter(low=10, high=100, default=25, space="buy", optimize=True)
    buy_emarsi1 = IntParameter(low=10, high=100, default=20, space="buy", optimize=True)
    buy_adx2 = IntParameter(low=20, high=100, default=30, space="buy", optimize=True)
    buy_emarsi2 = IntParameter(low=20, high=100, default=20, space="buy", optimize=True)
    buy_adx3 = IntParameter(low=10, high=100, default=35, space="buy", optimize=True)
    buy_emarsi3 = IntParameter(low=10, high=100, default=20, space="buy", optimize=True)
    buy_adx4 = IntParameter(low=20, high=100, default=30, space="buy", optimize=True)
    buy_emarsi4 = IntParameter(low=20, high=100, default=25, space="buy", optimize=True)

    # buy_1_enable = CategoricalParameter([True, False], default=True, space='buy', optimize=True)
    # buy_2_enable = CategoricalParameter([True, False], default=True, space='buy', optimize=True)
    # buy_3_enable = CategoricalParameter([True, False], default=True, space='buy', optimize=True)
    # buy_4_enable = CategoricalParameter([True, False], default=True, space='buy', optimize=True)
    #
    # sell_1_enable = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    # sell_2_enable = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    # sell_3_enable = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    # sell_4_enable = CategoricalParameter([True, False], default=True, space='sell', optimize=True)
    # sell_5_enable = CategoricalParameter([True, False], default=True, space='sell', optimize=True)

    leverage_optimize = False
    leverage_num = IntParameter(low=1, high=3, default=3, space="buy", optimize=leverage_optimize)

    # sell params
    adx2 = IntParameter(low=10, high=100, default=30, space="sell", optimize=True)
    emarsi1 = IntParameter(low=10, high=100, default=75, space="sell", optimize=True)
    emarsi2 = IntParameter(low=20, high=100, default=80, space="sell", optimize=True)
    emarsi3 = IntParameter(low=20, high=100, default=75, space="sell", optimize=True)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的各种技术指标：
        1. RSI和EMA-RSI：用于识别超买超卖状态
        2. ADX和方向指标：用于判断趋势强度和方向
        3. 多种移动平均线：用于趋势识别
        4. 趋势状态指标：用于判断市场趋势变化

        Args:
            dataframe: OHLCV价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了技术指标的数据框
        """
        # RSI相关指标
        dataframe["rsi"] = numpy.nan_to_num(ta.RSI(dataframe, timeperiod=5))  # 5周期RSI
        rsiframe = DataFrame(dataframe["rsi"]).rename(columns={"rsi": "close"})
        dataframe["emarsi"] = numpy.nan_to_num(ta.EMA(rsiframe, timeperiod=5))  # RSI的5周期EMA平滑

        # ADX趋势强度指标系统
        dataframe["adx"] = numpy.nan_to_num(ta.ADX(dataframe))  # 平均趋向指数，衡量趋势强度
        dataframe["minusdi"] = numpy.nan_to_num(ta.MINUS_DI(dataframe))  # 负方向指标
        minusdiframe = DataFrame(dataframe["minusdi"]).rename(columns={"minusdi": "close"})
        dataframe["minusdiema"] = numpy.nan_to_num(
            ta.EMA(minusdiframe, timeperiod=25)
        )  # 负方向指标的25周期EMA
        dataframe["plusdi"] = numpy.nan_to_num(ta.PLUS_DI(dataframe))  # 正方向指标
        plusdiframe = DataFrame(dataframe["plusdi"]).rename(columns={"plusdi": "close"})
        dataframe["plusdiema"] = numpy.nan_to_num(
            ta.EMA(plusdiframe, timeperiod=5)
        )  # 正方向指标的5周期EMA

        # 移动平均线系统
        dataframe["lowsma"] = numpy.nan_to_num(
            ta.EMA(dataframe, timeperiod=60)
        )  # 60周期EMA（短期支撑阻力）
        dataframe["highsma"] = numpy.nan_to_num(
            ta.EMA(dataframe, timeperiod=120)
        )  # 120周期EMA（中期支撑阻力）
        dataframe["fastsma"] = numpy.nan_to_num(
            ta.SMA(dataframe, timeperiod=120)
        )  # 120周期SMA（快速趋势线）
        dataframe["slowsma"] = numpy.nan_to_num(
            ta.SMA(dataframe, timeperiod=240)
        )  # 240周期SMA（慢速趋势线）

        # 趋势状态判断指标
        dataframe["bigup"] = dataframe["fastsma"].gt(dataframe["slowsma"]) & (
            (dataframe["fastsma"] - dataframe["slowsma"]) > dataframe["close"] / 300
        )  # 强上升趋势：快线高于慢线且差值足够大
        dataframe["bigdown"] = ~dataframe["bigup"]  # 非强上升趋势状态

        # 趋势变化检测指标
        dataframe["trend"] = dataframe["fastsma"] - dataframe["slowsma"]  # 趋势强度：快慢线差值
        dataframe["preparechangetrend"] = dataframe["trend"].gt(
            dataframe["trend"].shift()
        )  # 趋势准备转向
        dataframe["preparechangetrendconfirm"] = dataframe["preparechangetrend"] & dataframe[
            "trend"
        ].shift().gt(dataframe["trend"].shift(2))  # 趋势转向确认
        dataframe["continueup"] = dataframe["slowsma"].gt(dataframe["slowsma"].shift()) & dataframe[
            "slowsma"
        ].shift().gt(dataframe["slowsma"].shift(2))  # 持续上升：慢线连续2期上升

        # 动量变化指标
        dataframe["delta"] = dataframe["fastsma"] - dataframe["fastsma"].shift()  # 快线变化量
        dataframe["slowingdown"] = dataframe["delta"].lt(dataframe["delta"].shift())  # 动量减缓

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场趋势信号

        实现4种不同的买入策略，每种策略针对不同的市场状态：
        1. buy_1: 下降趋势中的反弹买入（非持续上升，非趋势转向）
        2. buy_2: 下降趋势中的持续上升买入
        3. buy_3: 上升趋势中的回调买入（非持续上升）
        4. buy_4: 上升趋势中的持续上升买入

        所有策略的共同条件：
        - 价格低于中短期移动平均线（超卖状态）
        - 负方向指标强于其平滑值（下跌动能）
        - RSI不再下降（动能转向）
        - ADX足够高（趋势强度足够）
        - EMA-RSI超卖

        Args:
            dataframe: 包含技术指标的价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # 买入策略1：下降趋势中的反弹买入
        # 适用场景：市场处于下降趋势，但没有持续上升，也没有趋势转向迹象
        buy_1 = (
            # self.buy_1_enable.value &  # 策略开关（已注释）
            dataframe["slowsma"].gt(0)  # 慢速移动平均线有效（大于0）
            & dataframe["close"].lt(dataframe["highsma"])  # 价格低于120周期EMA（中期阻力）
            & dataframe["close"].lt(dataframe["lowsma"])  # 价格低于60周期EMA（短期阻力）
            & dataframe["minusdi"].gt(
                dataframe["minusdiema"]
            )  # 负方向指标强于其平滑值（下跌动能强）
            & dataframe["rsi"].ge(dataframe["rsi"].shift())  # RSI不再下降（动能转向）
            & ~dataframe["preparechangetrend"]  # 没有趋势转向准备
            & ~dataframe["continueup"]  # 没有持续上升
            & dataframe["adx"].gt(self.buy_adx1.value)  # ADX高于阈值（趋势强度足够）
            & dataframe["bigdown"]  # 处于非强上升趋势状态
            & dataframe["emarsi"].le(self.buy_emarsi1.value)  # EMA-RSI超卖
        )

        # 买入策略2：下降趋势中的持续上升买入
        # 适用场景：市场处于下降趋势，但出现持续上升迹象
        buy_2 = (
            # self.buy_2_enable.value &  # 策略开关（已注释）
            dataframe["slowsma"].gt(0)  # 慢速移动平均线有效
            & dataframe["close"].lt(dataframe["highsma"])  # 价格低于中期阻力
            & dataframe["close"].lt(dataframe["lowsma"])  # 价格低于短期阻力
            & dataframe["minusdi"].gt(dataframe["minusdiema"])  # 下跌动能强
            & dataframe["rsi"].ge(dataframe["rsi"].shift())  # RSI动能转向
            & ~dataframe["preparechangetrend"]  # 没有趋势转向准备
            & dataframe["continueup"]  # 出现持续上升
            & dataframe["adx"].gt(self.buy_adx2.value)  # 趋势强度足够
            & dataframe["bigdown"]  # 非强上升趋势状态
            & dataframe["emarsi"].le(self.buy_emarsi2.value)  # EMA-RSI超卖
        )

        # 买入策略3：上升趋势中的回调买入
        # 适用场景：市场处于上升趋势，出现回调但没有持续上升
        buy_3 = (
            # self.buy_3_enable.value &  # 策略开关（已注释）
            dataframe["slowsma"].gt(0)  # 慢速移动平均线有效
            & dataframe["close"].lt(dataframe["highsma"])  # 价格低于中期阻力（回调）
            & dataframe["close"].lt(dataframe["lowsma"])  # 价格低于短期阻力（回调）
            & dataframe["minusdi"].gt(dataframe["minusdiema"])  # 下跌动能强（回调中）
            & dataframe["rsi"].ge(dataframe["rsi"].shift())  # RSI动能转向
            & ~dataframe["continueup"]  # 没有持续上升
            & dataframe["adx"].gt(self.buy_adx3.value)  # 趋势强度足够
            & dataframe["bigup"]  # 处于强上升趋势状态
            & dataframe["emarsi"].le(self.buy_emarsi3.value)  # EMA-RSI超卖
        )

        # 买入策略4：上升趋势中的持续上升买入
        # 适用场景：市场处于上升趋势，且出现持续上升迹象
        buy_4 = (
            # self.buy_4_enable.value &  # 策略开关（已注释）
            dataframe["slowsma"].gt(0)  # 慢速移动平均线有效
            & dataframe["close"].lt(dataframe["highsma"])  # 价格仍低于中期阻力
            & dataframe["close"].lt(dataframe["lowsma"])  # 价格仍低于短期阻力
            & dataframe["minusdi"].gt(dataframe["minusdiema"])  # 下跌动能强（但在上升趋势中）
            & dataframe["rsi"].ge(dataframe["rsi"].shift())  # RSI动能转向
            & dataframe["continueup"]  # 出现持续上升
            & dataframe["adx"].gt(self.buy_adx4.value)  # 趋势强度足够
            & dataframe["bigup"]  # 处于强上升趋势状态
            & dataframe["emarsi"].le(self.buy_emarsi4.value)  # EMA-RSI超卖
        )

        conditions.append(buy_1)
        dataframe.loc[buy_1, "enter_tag"] += "buy_1"

        conditions.append(buy_2)
        dataframe.loc[buy_2, "enter_tag"] += "buy_2"

        conditions.append(buy_3)
        dataframe.loc[buy_3, "enter_tag"] += "buy_3"

        conditions.append(buy_4)
        dataframe.loc[buy_4, "enter_tag"] += "buy_4"

        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        dataframe.loc[:, "exit_tag"] = ""

        s1 = (
            # self.sell_1_enable.value &
            ~dataframe["preparechangetrendconfirm"]
            & ~dataframe["continueup"]
            & (
                dataframe["close"].gt(dataframe["lowsma"])
                | dataframe["close"].gt(dataframe["highsma"])
            )
            & dataframe["highsma"].gt(0)
            & dataframe["bigdown"]
        )

        s2 = (
            # self.sell_2_enable.value &
            ~dataframe["preparechangetrendconfirm"]
            & ~dataframe["continueup"]
            & dataframe["close"].gt(dataframe["highsma"])
            & dataframe["highsma"].gt(0)
            & (
                dataframe["emarsi"].ge(self.emarsi1.value)
                | dataframe["close"].gt(dataframe["slowsma"])
            )
            & dataframe["bigdown"]
        )

        s3 = (
            # self.sell_3_enable.value &
            ~dataframe["preparechangetrendconfirm"]
            & dataframe["close"].gt(dataframe["highsma"])
            & dataframe["highsma"].gt(0)
            & dataframe["adx"].gt(self.adx2.value)
            & dataframe["emarsi"].ge(self.emarsi2.value)
            & dataframe["bigup"]
        )

        s4 = (
            # self.sell_4_enable.value &
            dataframe["preparechangetrendconfirm"]
            & ~dataframe["continueup"]
            & dataframe["slowingdown"]
            & dataframe["emarsi"].ge(self.emarsi3.value)
            & dataframe["slowsma"].gt(0)
        )

        s5 = (
            # self.sell_5_enable.value &
            dataframe["preparechangetrendconfirm"]
            & dataframe["minusdi"].lt(dataframe["plusdi"])
            & dataframe["close"].gt(dataframe["lowsma"])
            & dataframe["slowsma"].gt(0)
        )

        conditions.append(s1)
        dataframe.loc[s1, "exit_tag"] += "s1 "

        conditions.append(s2)
        dataframe.loc[s2, "exit_tag"] += "s2 "

        conditions.append(s3)
        dataframe.loc[s3, "exit_tag"] += "s3 "

        conditions.append(s4)
        dataframe.loc[s4, "exit_tag"] += "s4 "

        conditions.append(s5)
        dataframe.loc[s5, "exit_tag"] += "s5 "

        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "exit_long"] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        优先从配置文件中读取杠杆设置，如果配置文件中没有设置则使用策略内部的超参数

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 优先从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", None)

        if config_leverage is not None:
            # 使用配置文件中的杠杆设置
            leverage = config_leverage
        else:
            # 如果配置文件中没有设置，使用策略内部的超参数
            leverage = self.leverage_num.value

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(leverage, max_leverage))

        return leverage
