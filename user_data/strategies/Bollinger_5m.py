from datetime import datetime

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IStrategy
from pandas import DataFrame


class Bollinger_5m(IStrategy):
    """
    布林带变化率交易策略

    基于布林带轨道变化率进行双向交易的策略。

    策略核心逻辑：
    1. 做多条件：布林带上轨和中轨同时向上变化，变化率超过阈值
    2. 做空条件：布林带下轨和中轨同时向下变化，变化率超过阈值
    3. 退出条件：价格回归到布林带中轨附近

    技术指标：
    - 布林带：20周期，2倍标准差的布林带
    - 变化率计算：(当前值 - 前一周期值) / 当前值
    - 上轨变化率：bbu_change
    - 中轨变化率：bbm_change
    - 下轨变化率：bbl_change

    入场逻辑：
    - 做多：当布林带上轨和中轨都向上变化且变化率大于阈值时入场
    - 做空：当布林带下轨和中轨都向下变化且变化率小于阈值时入场
    - 变化率阈值通过超参数优化确定最佳值

    风险控制：
    - 支持双向交易（做多和做空）
    - 15%固定止损保护
    - 基于布林带中轨的退出机制
    - 极端盈利/亏损保护
    - 时间止损机制

    适用场景：
    - 波动率突然增加的市场
    - 布林带快速扩张或收缩时的交易机会
    - 短期动量交易

    作者: FreqTrade用户
    时间框架: 5分钟
    """

    # 基础设置
    timeframe = "5m"  # 主时间框架
    can_short = True  # 启用做空交易

    max_open_trades = 10

    # 基础风险参数
    buy_params = {
        "buy_bbl_change_threshold": -0.001,
        "buy_bbl_prev_change_threshold": 0.0,
        "buy_bbu_change_threshold": 0.001,
        "buy_bbu_prev_change_threshold": 0.001,
        "buy_leverage": 3.0,
    }
    sell_params = {
        # "sell_exit_bb_middle_threshold": 0.995,
    }

    stoploss = -0.326
    minimal_roi = {}
    trailing_stop = True
    trailing_stop_positive = 0.314
    trailing_stop_positive_offset = 0.316
    trailing_only_offset_is_reached = True

    # 杠杆设置
    buy_leverage = DecimalParameter(1.0, 5.0, default=3.0, space="buy", optimize=False)  # 杠杆倍数（1-5倍）

    # 布林带变化率参数
    buy_bbu_change_threshold = DecimalParameter(
        0.0001, 0.005, default=0.001, space="buy", optimize=True
    )  # 做多布林带变化率阈值（正值，表示向上变化）
    buy_bbl_change_threshold = DecimalParameter(
        -0.005, -0.0001, default=-0.001, space="buy", optimize=True
    )  # 做空布林带变化率阈值（负值，表示向下变化）

    # 前一周期布林带中轨变化率过滤参数
    buy_bbu_prev_change_threshold = DecimalParameter(0, 0.001, default=0.00005, space="buy", optimize=True)  # 做多时前一周期中轨变化率上限阈值
    buy_bbl_prev_change_threshold = DecimalParameter(-0.001, 0, default=-0.00005, space="buy", optimize=True)  # 做空时前一周期中轨变化率下限阈值

    # 退出参数
    # sell_exit_bb_middle_threshold = DecimalParameter(0.995, 1.005, default=1.0, space="sell", optimize=True)  # 布林带中轨退出阈值

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        # 计算布林带（20周期，2倍标准差）
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]  # 布林带下轨
        df["bb_middleband"] = bollinger["mid"]  # 布林带中轨（移动平均线）
        df["bb_upperband"] = bollinger["upper"]  # 布林带上轨

        # 计算布林带各轨道的变化率（当前值相对于前一周期的变化百分比）
        df["bbl_change"] = (df["bb_lowerband"] - df["bb_lowerband"].shift(1)) / df["bb_lowerband"]  # 下轨变化率
        df["bbm_change"] = (df["bb_middleband"] - df["bb_middleband"].shift(1)) / df["bb_middleband"]  # 中轨变化率
        df["bbu_change"] = (df["bb_upperband"] - df["bb_upperband"].shift(1)) / df["bb_upperband"]  # 上轨变化率

        df["bbl_sma"] = df["bb_lowerband"].rolling(window=3, min_periods=1).mean()
        df["bbm_sma"] = df["bb_middleband"].rolling(window=3, min_periods=1).mean()
        df["bbu_sma"] = df["bb_upperband"].rolling(window=3, min_periods=1).mean()

        df["bbl_sma_ratio"] = (df["bb_lowerband"] - df["bbl_sma"]) / df["bb_lowerband"]
        df["bbm_sma_ratio"] = (df["bb_middleband"] - df["bbm_sma"]) / df["bb_middleband"]
        df["bbu_sma_ratio"] = (df["bb_upperband"] - df["bbu_sma"]) / df["bb_upperband"]

        df["ema_200"] = df["close"].ewm(span=200, adjust=False).mean()  # 200周期EMA慢线
        # 辅助指标
        df["zero"] = 0  # 零线，用于图表显示
        return df

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成入场信号

        基于布林带变化率生成做多和做空信号：
        1. 做多信号：布林带上轨和中轨同时向上变化，变化率超过阈值
        2. 做空信号：布林带下轨和中轨同时向下变化，变化率超过阈值

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        # 基础条件：确保有交易量
        base_condition = (
            True  # 始终为真的占位符
            & (df["volume"] > 0)  # 确保有交易量，避免在无交易时段入场
        )

        # 做多入场条件：布林带向上扩张
        # 当布林带上轨和中轨都向上变化且变化率大于阈值时，表示波动率增加且价格向上突破
        df.loc[
            (
                base_condition  # 基本条件
                & (df["close"] < df["bb_upperband_5m"])  # 收盘价高于下轨
                & (df["bbu_change"] > 0)  # 上轨向上变化率大于阈值
                & (
                    (df["bbu_change"] > self.buy_bbu_change_threshold.value)  #
                    | (df["bbm_change"] > self.buy_bbu_change_threshold.value)
                )
                & (
                    (df["bbm_change"].shift(1) < self.buy_bbu_prev_change_threshold.value)  #
                    | (df["bbu_change"].shift(1) < self.buy_bbu_prev_change_threshold.value)
                )
            ),
            ["enter_long", "enter_tag"],
        ] = (1, "bb_expand_up")  # 标记为布林带向上扩张入场

        # 做空入场条件：布林带向下扩张
        # 当布林带下轨和中轨都向下变化且变化率小于阈值时，表示波动率增加且价格向下突破
        df.loc[
            (
                base_condition  # 基本条件
                & (df["close"] > df["bb_lowerband_5m"])  # 收盘价低于上轨
                & (df["bbl_change"] < 0)  # 下轨向下变化率小于阈值（负值）
                & (
                    (df["bbm_change"] < self.buy_bbl_change_threshold.value)  #
                    | (df["bbl_change"] < self.buy_bbl_change_threshold.value)
                )
                & (
                    (df["bbm_change"].shift(1) > self.buy_bbl_prev_change_threshold.value)  #
                    | (df["bbl_change"].shift(1) > self.buy_bbl_prev_change_threshold.value)
                )
            ),
            ["enter_short", "enter_tag"],
        ] = (1, "bb_expand_down")  # 标记为布林带向下扩张入场

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成退出信号

        基于价格回归布林带中轨生成退出信号，当价格回到布林带中轨附近时退出仓位

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了退出信号的数据框
        """
        # # 多头退出条件：价格回到布林带中轨之上
        # # 当做多仓位的价格重新回到中轨上方时，表示上涨动能减弱，应该退出
        # exit_long_condition = df["close"] > df["bb_middleband"] * self.sell_exit_bb_middle_threshold.value

        # # 空头退出条件：价格回到布林带中轨之下
        # # 当做空仓位的价格重新回到中轨下方时，表示下跌动能减弱，应该退出
        # exit_short_condition = df["close"] < df["bb_middleband"] * self.sell_exit_bb_middle_threshold.value

        # # 设置退出信号
        # df.loc[exit_long_condition, ["exit_long", "exit_tag"]] = (1, "bb_middle_return")
        # df.loc[exit_short_condition, ["exit_short", "exit_tag"]] = (1, "bb_middle_return")

        return df

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> str | None:
        """
        自定义退出逻辑

        提供额外的风险控制退出条件，补充基础的技术指标退出信号

        Args:
            pair: 交易对名称
            trade: 当前交易对象，包含交易信息
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利率（正数为盈利，负数为亏损）
            **kwargs: 其他参数

        Returns:
            str | None: 退出标签或None（不退出）
        """
        # 极端盈利保护：盈利超过20%时获利了结
        # 防止过度贪婪，及时锁定利润
        if current_profit > 0.20:
            return "extreme_profit_exit"

        # 极端亏损保护：亏损超过12%时强制止损
        # 防止亏损进一步扩大，保护资金安全
        if current_profit < -0.12:
            return "extreme_loss_exit"

        # 计算持仓时间
        holding_time = current_time - trade.open_date_utc
        holding_hours = holding_time.total_seconds() / 3600  # 转换为小时
        # 时间止损：持仓超过4小时且处于亏损状态时退出
        # 避免长时间持有亏损仓位，提高资金使用效率
        if current_profit < 0:
            if holding_hours >= 4.0:
                return "time_stop_exit"

        # dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)  # 获取分析后的数据
        # if len(dataframe) < 1:
        #     return None
        # df = dataframe.iloc[-1].squeeze()  # 获取最后一行数据

        # # 做多
        # if not trade.is_short:
        #     if current_profit < 0.001:
        #         if current_rate < df["bb_middleband"]:
        #             return "bb_middleband_exit"
        # # 做空
        # else:
        #     if current_profit < 0.001:
        #         if current_rate > df["bb_middleband"]:
        #             return "bb_middleband_exit"

        return None

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        设置杠杆倍数

        动态设置交易杠杆，优先从config.json读取全局设置，
        如无配置则使用策略内部的超参数优化值

        Args:
            pair: 交易对名称
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: FreqTrade建议的杠杆倍数
            max_leverage: 交易所允许的最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向（long/short）
            **kwargs: 其他参数

        Returns:
            float: 实际使用的杠杆倍数（1.0到max_leverage之间）
        """
        # 优先从config.json读取全局杠杆设置
        if hasattr(self.config, "get") and "trading" in self.config:
            config_leverage = self.config.get("trading", {}).get("leverage", None)
            if config_leverage is not None:
                return min(max(float(config_leverage), 1.0), max_leverage)

        # 如果config.json中没有设置，使用策略内部超参数
        return min(max(float(self.buy_leverage.value), 1.0), max_leverage)
