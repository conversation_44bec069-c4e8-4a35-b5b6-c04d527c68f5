import logging
import warnings
from datetime import datetime, timedelta
from functools import reduce
from typing import TYPE_CHECKING

import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.strategy import BooleanParameter, DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

if TYPE_CHECKING:
    from freqtrade.persistence import Trade

warnings.simplefilter(action="ignore", category=RuntimeWarning)

logger = logging.getLogger(__name__)


class E0V1E_Enhanced(IStrategy):
    """
    E0V1E 增强版多空策略

    策略核心思想:
    基于均值回归的5分钟短线多空策略，在多重技术指标确认的超卖/超买状态下入场，
    通过动态出场机制和严格的风险管理来获取短期反弹/回调收益。

    主要特性:
    1. 支持做多和做空双向交易
    2. 统一的入场逻辑，消除代码重复
    3. 优化的风险管理和动态止损
    4. 增强的技术指标和成交量确认
    5. 智能的出场逻辑和风险控制
    6. 市场环境过滤机制
    7. 全面参数化，支持多空独立优化
    """  # noqa: RUF002

    # === hyperopt ===
    optimize_entry = True
    optimize_leverage_threshold = True
    optimize_leverage_volume = True

    # === 基础配置 ===
    # Buy hyperspace params:
    buy_params = {
        # === 市场环境过滤参数 ===
        # 启用市场环境过滤器
        "buy_market_24h_change_enable": True,
        # 24小时价格变化范围
        "buy_market_24h_change_min": -20,
        "buy_market_24h_change_max": 9.2,
        # ======================
        #  === ATR波动性过滤器 ===
        # 启用ATR波动性过滤器
        "buy_atr_pct_enable": True,
        # 最低ATR波动率(>%)
        "buy_atr_pct_min": 1.0,
        # 最高ATR波动率(<%)
        "buy_atr_pct_max": 6.0,
        # ======================
        # === 成交量过滤器 ===
        # 启用成交量过滤器
        "buy_volume_enable": True,
        # 成交量异常系数(>)
        "buy_volume_factor": 1.0,
        # ======================
        # === 趋势强度过滤参数 ===
        "buy_adx_enable": True,
        "buy_adx_threshold": 30,
        # =======================
        "buy_cti": 0.05,
        # 入场RSI值(>)
        "buy_rsi": 21,
        "buy_rsi_alt": 22,
        # 入场快速RSI值(<)
        "buy_rsi_fast": 38,
        "buy_rsi_fast_alt": 27,
        # 入场SMA倍率(*<)
        "buy_sma_ratio": 0.978,
        "buy_sma_ratio_alt": 0.976,
        # =======================
        # === 动态杠杆调整超参数 ===
        "buy_leverage_main_signal_multiplier": 1.0,  # 主信号使用标准杠杆倍数
        "buy_leverage_alt_signal_multiplier": 0.4,  # 备用信号使用较低杠杆倍数
        # 低波动率(<)
        "buy_leverage_atr_low_threshold": 1.65,
        "buy_leverage_atr_low_vol": 5.0,  # 低波动时使用的杠杆
        # 高波动率(>)
        "buy_leverage_atr_high_threshold": 3.5,
        "buy_leverage_atr_high_vol": 2.0,  # 高波动时使用的杠杆
        # 正常波动时使用的杠杆
        "buy_leverage_atr_normal_vol": 3.0,
        # RSI极度超卖杠杆增强
        "buy_leverage_rsi_extreme_enable": True,
        "buy_leverage_rsi_extreme_threshold": 21.0,  # RSI极度超卖值(<)
        "buy_leverage_rsi_extreme_boost_factor": 1.4,  # RSI极度超卖杠杆增强倍数(*)
        # === 成交量异常调整超参数 ===
        # 成交量异常杠杆调整
        "buy_leverage_volume_enable": True,
        "buy_leverage_volume_high_threshold": 2.75,  # 成交量异常阈值(>)
        "buy_leverage_volume_high_reduction_factor": 0.8,  # 成交量异常杠杆降低倍数(*)
    }

    # Sell hyperspace params:
    sell_params = {
        # 如果时间>小时
        "sell_max_hold_hours": 11,
        "sell_max_hold_profit_threshold": -0.052,  # 利润(>%)
        # 如果利润>%
        "sell_profit_protection_high_threshold": 0.08,  # fastk(>)
        "sell_profit_protection_high_fastk": 75,
        #
        "sell_cci_loss": 75,
        "sell_profit_protection_normal_fastk": 71,
        "sell_rsi_peak": 81,
        "sell_atr_max_pct": 6.0,
    }

    # Protection hyperspace params:
    protection_params = {
        # 冷却期保护参数
        "protection_cooldown_enable": False,
        "protection_cooldown_stop_duration": 48,
        # 止损保护参数
        "protection_stoploss_enable": True,
        "protection_stoploss_lookback_period": 24,  # 回顾期间（K线数）
        "protection_stoploss_trade_limit": 1,  # 触发保护的止损交易数量
        "protection_stoploss_stop_duration": 60,  # 暂停交易的时间（K线数）
        # 最大回撤保护参数
        "protection_maxdrawdown_enable": False,
        "protection_maxdrawdown_lookback_period": 144,  # 回顾期间（K线数）
        "protection_maxdrawdown_trade_limit": 5,  # 最小交易数量要求
        "protection_maxdrawdown_stop_duration": 120,  # 暂停交易的时间（K线数）
        "protection_maxdrawdown_max_allowed": 0.10,  # 最大允许回撤比例
        # 低利润保护参数
        "protection_lowprofit_enable": False,
        "protection_lowprofit_lookback_period": 288,  # 回顾期间（K线数）
        "protection_lowprofit_trade_limit": 8,  # 最小交易数量要求
        "protection_lowprofit_stop_duration": 240,  # 暂停交易的时间（K线数）
        "protection_lowprofit_required_profit": 0.01,  # 要求的最小利润比例
        # === 交易保护机制超参数 ===
    }

    minimal_roi = {}  # 禁用基于时间的ROI，完全依赖动态出场
    timeframe = "5m"
    process_only_new_candles = True
    startup_candle_count = 300

    # === 订单配置 ===
    order_types = {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_entry": "market",
        "force_exit": "market",
        "stoploss": "market",
        "stoploss_on_exchange": False,
        "stoploss_on_exchange_interval": 60,
        "stoploss_on_exchange_market_ratio": 0.99,
    }

    # === 风险管理配置 ===
    stoploss = -0.1  # 统一止损水平

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.03  # 当价格从最高点回落0.3%时，触发追踪止盈
    trailing_stop_positive_offset = 0.15  # 利润达到15%后，才激活追踪止损
    trailing_only_offset_is_reached = True

    use_custom_stoploss = False

    # === 策略开关 ===
    buy_market_24h_change_enable = BooleanParameter(default=True, space="buy", optimize=False)
    buy_volume_enable = BooleanParameter(default=True, space="buy", optimize=False)
    buy_atr_pct_enable = BooleanParameter(default=True, space="buy", optimize=False)

    # === 市场环境过滤参数 ===
    buy_market_24h_change_min = DecimalParameter(-25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=optimize_entry)
    buy_market_24h_change_max = DecimalParameter(5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=optimize_entry)

    # ATR波动性过滤
    buy_atr_pct_min = DecimalParameter(0.3, 1.0, default=0.5, decimals=1, space="buy", optimize=optimize_entry)
    buy_atr_pct_max = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="buy", optimize=optimize_entry)

    # 成交量过滤
    buy_volume_factor = DecimalParameter(1.0, 2.0, default=1.2, decimals=1, space="buy", optimize=optimize_entry)

    buy_adx_enable = BooleanParameter(default=True, space="buy", optimize=False)
    buy_adx_threshold = IntParameter(10, 50, default=30, space="buy", optimize=optimize_entry)

    # === 入场信号参数 ===
    buy_rsi_fast = IntParameter(20, 50, default=35, space="buy", optimize=optimize_entry)
    buy_rsi = IntParameter(15, 45, default=30, space="buy", optimize=optimize_entry)
    buy_sma_ratio = DecimalParameter(0.92, 0.99, default=0.96, decimals=3, space="buy", optimize=optimize_entry)
    buy_cti = DecimalParameter(-1.0, 0.5, default=-0.2, decimals=2, space="buy", optimize=optimize_entry)

    # === 备用入场信号参数 ===
    buy_rsi_fast_alt = IntParameter(25, 45, default=34, space="buy", optimize=optimize_entry)
    buy_rsi_alt = IntParameter(20, 35, default=28, space="buy", optimize=optimize_entry)
    buy_sma_ratio_alt = DecimalParameter(0.94, 0.98, default=0.96, decimals=3, space="buy", optimize=optimize_entry)

    # === 出场信号参数 ===
    sell_profit_protection_normal_fastk = IntParameter(70, 95, default=84, space="sell", optimize=True)
    sell_cci_loss = IntParameter(60, 100, default=80, space="sell", optimize=True)
    sell_rsi_peak = IntParameter(65, 85, default=75, space="sell", optimize=True)
    sell_atr_max_pct = DecimalParameter(3.0, 8.0, default=5.0, decimals=1, space="sell", optimize=optimize_entry)

    # 时间止损参数
    sell_max_hold_hours = IntParameter(4, 24, default=8, space="sell", optimize=True)
    sell_max_hold_profit_threshold = DecimalParameter(-0.08, -0.02, default=-0.05, decimals=3, space="sell", optimize=True)

    # 利润保护参数
    sell_profit_protection_high_threshold = DecimalParameter(0.03, 0.08, default=0.05, decimals=3, space="sell", optimize=True)
    sell_profit_protection_high_fastk = IntParameter(65, 85, default=75, space="sell", optimize=True)

    # === 动态杠杆调整超参数 ===
    # 基础杠杆设置
    buy_leverage_atr_normal_vol = DecimalParameter(1.0, 3.0, default=2.0, decimals=1, space="buy", optimize=optimize_leverage_volume)
    buy_leverage_atr_low_vol = DecimalParameter(2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=optimize_leverage_volume)
    buy_leverage_atr_high_vol = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=optimize_leverage_volume)
    # 波动性阈值
    buy_leverage_atr_low_threshold = DecimalParameter(1.0, 2.0, default=1.5, decimals=1, space="buy", optimize=optimize_leverage_threshold)
    buy_leverage_atr_high_threshold = DecimalParameter(3.5, 5.5, default=3.5, decimals=1, space="buy", optimize=optimize_leverage_threshold)

    # 信号类型杠杆调整系数
    buy_leverage_main_signal_multiplier = DecimalParameter(1.0, 1.5, default=1.0, decimals=2, space="buy", optimize=optimize_leverage_volume)
    buy_leverage_alt_signal_multiplier = DecimalParameter(0.1, 0.5, default=0.33, decimals=2, space="buy", optimize=optimize_leverage_volume)

    # RSI极值杠杆调整
    buy_leverage_rsi_extreme_enable = BooleanParameter(default=True, space="buy", optimize=False)
    buy_leverage_rsi_extreme_threshold = DecimalParameter(15.0, 28.0, default=20.0, space="buy", optimize=optimize_leverage_threshold)
    buy_leverage_rsi_extreme_boost_factor = DecimalParameter(1.2, 1.6, default=1.3, decimals=2, space="buy", optimize=optimize_leverage_volume)

    # 成交量杠杆调整
    buy_leverage_volume_enable = BooleanParameter(default=True, space="buy", optimize=False)
    buy_leverage_volume_high_threshold = DecimalParameter(2.0, 4.0, default=3.0, decimals=1, space="buy", optimize=optimize_leverage_threshold)
    buy_leverage_volume_high_reduction_factor = DecimalParameter(0.4, 0.8, default=0.6, decimals=2, space="buy", optimize=optimize_leverage_volume)

    # === 动态仓位管理超参数 ===
    stake_main_multiplier = DecimalParameter(0.8, 1.2, default=1.0, decimals=2, space="buy", optimize=True)
    stake_alt_multiplier = DecimalParameter(0.5, 0.9, default=0.7, decimals=2, space="buy", optimize=True)
    stake_vol_high_multiplier = DecimalParameter(0.8, 1.2, default=1.0, decimals=2, space="buy", optimize=True)
    stake_vol_low_multiplier = DecimalParameter(1.0, 2.5, default=2.0, decimals=2, space="buy", optimize=True)
    stake_rsi_low_multiplier = DecimalParameter(1.0, 1.5, default=1.2, decimals=2, space="buy", optimize=True)

    # === 交易保护机制超参数 ===
    # StoplossGuard 保护参数
    protection_stoploss_enable = BooleanParameter(default=True, space="protection", optimize=False)
    protection_stoploss_lookback_period = IntParameter(4, 20, default=12, space="protection", optimize=protection_stoploss_enable.value)
    protection_stoploss_trade_limit = IntParameter(1, 3, default=1, space="protection", optimize=protection_stoploss_enable.value)
    protection_stoploss_stop_duration = IntParameter(12, 40, default=20, space="protection", optimize=protection_stoploss_enable.value)

    # MaxDrawdown 保护参数
    protection_maxdrawdown_enable = BooleanParameter(default=True, space="protection", optimize=False)
    protection_maxdrawdown_lookback_period = IntParameter(48, 288, default=144, space="protection", optimize=protection_maxdrawdown_enable.value)
    protection_maxdrawdown_trade_limit = IntParameter(2, 10, default=5, space="protection", optimize=protection_maxdrawdown_enable.value)
    protection_maxdrawdown_stop_duration = IntParameter(60, 240, default=120, space="protection", optimize=protection_maxdrawdown_enable.value)
    protection_maxdrawdown_max_allowed = DecimalParameter(
        0.05,
        0.20,
        default=0.10,
        decimals=2,
        space="protection",
        optimize=protection_maxdrawdown_enable.value,
    )

    # # LowProfitPairs 保护参数
    # protection_lowprofit_enable = BooleanParameter(default=True, space="protection", optimize=False)
    # protection_lowprofit_lookback_period = IntParameter(
    #     96, 576, default=288, space="protection", optimize=protection_lowprofit_enable.value
    # )
    # protection_lowprofit_trade_limit = IntParameter(
    #     3, 15, default=8, space="protection", optimize=protection_lowprofit_enable.value
    # )
    # protection_lowprofit_stop_duration = IntParameter(
    #     120, 480, default=240, space="protection", optimize=protection_lowprofit_enable.value
    # )
    # protection_lowprofit_required_profit = DecimalParameter(
    #     -0.02,
    #     0.05,
    #     default=0.01,
    #     decimals=3,
    #     space="protection",
    #     optimize=protection_lowprofit_enable.value,
    # )

    @property
    def protections(self):
        """
        动态交易保护机制 - 基于超参数优化的智能保护策略

        保护机制说明:
        1. CooldownPeriod - 交易冷却期，避免频繁交易
        2. StoplossGuard - 止损后暂停交易，防止连续亏损
        3. MaxDrawdown - 最大回撤保护，控制整体风险
        4. LowProfitPairs - 低利润交易对保护，避免无效交易
        """
        protection_list = []

        # 保护机制 2: 止损后暂停交易
        if self.protection_stoploss_enable.value:
            protection_list.append(
                {
                    "method": "StoplossGuard",
                    # 回顾期间（K线数）
                    "lookback_period_candles": self.protection_stoploss_lookback_period.value,
                    # 触发保护的止损交易数量
                    "trade_limit": self.protection_stoploss_trade_limit.value,
                    # 暂停交易的时间（K线数）
                    "stop_duration_candles": self.protection_stoploss_stop_duration.value,
                    # 此规则仅应用于当前交易对
                    "only_per_pair": True,
                }
            )

        return protection_list

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        """计算技术指标"""

        # === RSI系列指标 ===
        df["rsi"] = ta.RSI(df, timeperiod=14)
        df["rsi_fast"] = ta.RSI(df, timeperiod=4)
        df["rsi_slow"] = ta.RSI(df, timeperiod=20)

        # === 移动平均线 ===
        df["sma_15"] = ta.SMA(df, timeperiod=15)
        df["ema_20"] = ta.EMA(df, timeperiod=20)

        # === 趋势和动量指标 ===
        df["cti"] = pta.cti(df["close"], length=20)
        df["cci"] = ta.CCI(df, timeperiod=20)

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(df, 5, 3, 0, 3, 0)
        df["fastk"] = stoch_fast["fastk"]
        df["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        df["atr"] = ta.ATR(df, timeperiod=14)
        df["atr_pct"] = (df["atr"] / df["close"]) * 100

        # === 成交量指标 ===
        df["volume_sma"] = ta.SMA(df["volume"], timeperiod=20)
        df["volume_ratio"] = df["volume"] / df["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        df["24h_change_pct"] = ((df["close"] - df["close"].shift(288)) / df["close"].shift(288)) * 100

        # === 布林带指标 ===
        bollinger = ta.BBANDS(df, timeperiod=20, nbdevup=2.0, nbdevdn=2.0, matype=0)
        df["bb_lowerband"] = bollinger["lowerband"]
        df["bb_middleband"] = bollinger["middleband"]
        df["bb_upperband"] = bollinger["upperband"]
        df["bb_percent"] = (df["close"] - df["bb_lowerband"]) / (df["bb_upperband"] - df["bb_lowerband"])

        # === ADX 指标 ===
        df["adx"] = ta.ADX(df)

        return df

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """定义多空入场信号"""
        long_conditions = []
        df.loc[:, "enter_tag"] = ""

        # === 市场环境过滤器 ===
        market_filter = True
        if self.buy_market_24h_change_enable.value:
            market_filter = (
                (df["24h_change_pct"].notna())
                & (df["24h_change_pct"] > self.buy_market_24h_change_min.value)
                & (df["24h_change_pct"] < self.buy_market_24h_change_max.value)
            )

        # === ATR波动性过滤器 ===
        atr_filter = True
        # if self.buy_atr_pct_enable.value:
        #     atr_filter = (df["atr_pct"] > self.buy_atr_pct_min.value) & (
        #         df["atr_pct"] < self.buy_atr_pct_max.value
        #     )

        # === 成交量过滤器 ===
        volume_filter = True
        # if self.buy_volume_enable.value:
        #     volume_filter = df["volume_ratio"] > self.buy_volume_factor.value

        # === 趋势强度过滤器 ===
        trend_filter = True
        if self.buy_adx_enable.value:
            trend_filter = df["adx"] < self.buy_adx_threshold.value

        # === 统一的趋势条件 ===
        rsi_trend_condition = df["rsi_slow"] < df["rsi_slow"].shift(1)

        # === 基础过滤条件组合 ===
        base_filter = market_filter & atr_filter & volume_filter & trend_filter & rsi_trend_condition

        # 主信号: 统一的超卖反弹逻辑
        main_long_entry = (
            base_filter
            & (df["rsi_fast"] < self.buy_rsi_fast.value)
            & (df["rsi"] > self.buy_rsi.value)
            & (df["close"] < df["sma_15"] * self.buy_sma_ratio.value)
            & (df["cti"] < self.buy_cti.value)
            & (df["bb_percent"] < 0.2)  # 接近布林带下轨
        )
        long_conditions.append(main_long_entry)
        df.loc[main_long_entry, "enter_tag"] = "main_long"

        # 备用信号: 更激进的抄底
        alt_long_entry = (
            base_filter
            & (df["rsi_fast"] < self.buy_rsi_fast_alt.value)
            & (df["rsi"] > self.buy_rsi_alt.value)
            & (df["close"] < df["sma_15"] * self.buy_sma_ratio_alt.value)
            & (df["cti"] < self.buy_cti.value)
            & (df["close"] < df["bb_lowerband"])  # 突破布林带下轨
        )
        long_conditions.append(alt_long_entry)
        df.loc[alt_long_entry, "enter_tag"] += " alt_long"

        # === 应用入场条件 ===
        if long_conditions:
            df.loc[reduce(lambda x, y: x | y, long_conditions), "enter_long"] = 1

        return df

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        动态多空出场逻辑
        根据不同的入场原因和交易方向实现智能出场
        """
        df, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = df.iloc[-1].squeeze()

        # === 时间止损检查 ===
        if current_time - trade.open_date_utc > timedelta(hours=self.sell_max_hold_hours.value):
            if current_profit > self.sell_max_hold_profit_threshold.value:
                return "max_hold_hours"

        # 盈利时的出场策略
        if current_profit > 0:
            # 高利润保护机制
            if current_profit > self.sell_profit_protection_high_threshold.value:
                if current_candle["fastk"] > self.sell_profit_protection_high_fastk.value:
                    return "high_profit_protection"

            # 标准盈利出场
            if current_candle["fastk"] > self.sell_profit_protection_normal_fastk.value:
                return "normal_profit_protection"

            # 针对主信号的特定出场
            if "main_long" in str(trade.enter_tag):
                if current_candle["rsi"] > self.sell_rsi_peak.value:
                    return "主信号保护"

            # # 针对备用信号的特定出场
            # if "alt_long" in str(trade.enter_tag):
            #     # 布林带回归中轨出场
            #     if current_rate > current_candle["bb_middleband"]:
            #         return "布林带回归中轨"

        # 风险控制出场
        # 小幅亏损时的保护性出场
        if current_profit > self.sell_max_hold_profit_threshold.value:
            if current_candle["cci"] > self.sell_cci_loss.value:
                return "小幅亏损风险"

        # 趋势反转信号
        if current_profit > -0.03:  # 允许小幅亏损时触发
            if current_candle["rsi"] > self.sell_rsi_peak.value and current_candle["fastk"] > self.sell_profit_protection_high_fastk.value:
                return "趋势反转信号"

        # 波动性异常退出
        if current_candle["atr_pct"] > self.sell_atr_max_pct.value:
            if current_profit > -0.08:  # 允许较大亏损时触发
                return "波动性异常退出"

        # # 成交量异常退出
        # if current_candle["volume_ratio"] > 3.0:  # 异常放量
        #     if current_profit > -0.04:
        #         return "成交量异常退出"

        return None

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        静态出场信号（主要依赖custom_exit）
        """
        df.loc[:, ["exit_long", "exit_tag"]] = (0, None)
        return df

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        """
        交易确认函数 - 多空交易的最后一道风险控制
        """
        df, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if df is None or len(df) < 1:
            return False

        current_candle = df.iloc[-1].squeeze()

        # 确保关键指标存在且有效
        required_indicators = ["rsi", "rsi_fast", "atr_pct", "volume_ratio"]
        for indicator in required_indicators:
            if indicator not in current_candle or pd.isna(current_candle[indicator]):
                return False

        # 最终风险检查
        if current_candle["atr_pct"] > 10.0:  # 极端波动
            return False

        if current_candle["rsi"] < 10 or current_candle["rsi"] > 90:  # 极端RSI
            return False

        # 确保不在极端超买状态
        if current_candle["rsi"] > 80 and current_candle["fastk"] > 90:
            return False

        return True

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态杠杆调整 - 基于超参数优化的智能杠杆策略

        杠杆调整逻辑:
        0. 优先从config.json读取基础杠杆配置
        1. 根据市场波动性（ATR）调整基础杠杆
        2. 根据入场信号类型调整杠杆倍数
        3. 根据RSI极值情况进行杠杆增强
        4. 根据成交量异常情况降低杠杆
        5. 综合所有因素计算最终杠杆
        """
        df, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if df is None or len(df) < 1:
            return 1.0

        current_candle = df.iloc[-1].squeeze()

        # === 第一步: 根据波动性确定基础杠杆 ===
        # 优先从配置文件中读取基础杠杆设置
        config_leverage = self.config.get("leverage", None)

        if config_leverage is not None:
            # 使用配置文件中的杠杆设置作为基础杠杆
            base_leverage = config_leverage
        else:
            # 如果配置文件中没有设置，使用策略内部的超参数
            base_leverage = self.buy_leverage_atr_normal_vol.value  # 默认正常波动杠杆

        if "atr_pct" in current_candle and not pd.isna(current_candle["atr_pct"]):
            atr_pct = current_candle["atr_pct"]

            if atr_pct > self.buy_leverage_atr_high_threshold.value:
                # 高波动时使用低杠杆
                base_leverage = self.buy_leverage_atr_high_vol.value
            elif atr_pct < self.buy_leverage_atr_low_threshold.value:
                # 低波动时可以使用高杠杆
                base_leverage = self.buy_leverage_atr_low_vol.value

        # === 第二步: 根据入场信号类型调整杠杆 ===
        signal_multiplier = 1.0

        if "main_" in entry_tag:
            # 主信号使用标准杠杆倍数
            signal_multiplier = self.buy_leverage_main_signal_multiplier.value
        elif "alt_" in entry_tag:
            # 备用信号使用较低杠杆倍数
            signal_multiplier = self.buy_leverage_alt_signal_multiplier.value

        adjusted_leverage = base_leverage * signal_multiplier

        # # === 第三步: RSI极值杠杆增强 ===
        # if (
        #     self.buy_leverage_rsi_extreme_enable.value
        #     and "rsi" in current_candle
        #     and not pd.isna(current_candle["rsi"])
        # ):
        #     rsi = current_candle["rsi"]

        #     # RSI极度超卖可以增加杠杆
        #     if side == "long" and rsi < self.buy_leverage_rsi_extreme_threshold.value:
        #         adjusted_leverage *= self.buy_leverage_rsi_extreme_boost_factor.value

        # # === 第四步: 成交量异常杠杆调整 ===
        # if (
        #     self.buy_leverage_volume_enable.value
        #     and "volume_ratio" in current_candle
        #     and not pd.isna(current_candle["volume_ratio"])
        # ):
        #     volume_ratio = current_candle["volume_ratio"]

        #     # 异常放量时降低杠杆
        #     if volume_ratio > self.buy_leverage_volume_high_threshold.value:
        #         adjusted_leverage *= self.buy_leverage_volume_high_reduction_factor.value

        # === 第五步: 最终杠杆限制和安全检查 ===
        # 确保杠杆在合理范围内
        final_leverage = max(1.0, min(round(adjusted_leverage, 2), max_leverage))

        # 额外的安全检查
        if "atr_pct" in current_candle and current_candle["atr_pct"] > 8.0:
            # 极端波动时强制降低杠杆
            final_leverage = min(final_leverage, 1.5)

        return final_leverage

    def custom_stake_amount(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        动态仓位管理 - 支持多空不同仓位策略
        """
        df, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)

        if df is None or len(df) < 1:
            return proposed_stake

        current_candle = df.iloc[-1].squeeze()

        # 基础仓位
        base_stake = proposed_stake

        # 根据信号强度调整仓位
        if "main_" in entry_tag:
            # 主信号使用标准仓位
            position_multiplier = self.stake_main_multiplier.value
        elif "alt_" in entry_tag:
            # 备用信号使用较小仓位
            position_multiplier = self.stake_alt_multiplier.value
        else:
            position_multiplier = 0.8  # 默认值

        # 根据市场波动性调整仓位
        if "atr_pct" in current_candle:
            atr_pct = current_candle["atr_pct"]
            if atr_pct > 5.0:
                position_multiplier *= self.stake_vol_high_multiplier.value
            elif atr_pct < 1.0:
                position_multiplier *= self.stake_vol_low_multiplier.value

        # 根据RSI极值调整仓位
        if "rsi" in current_candle:
            rsi = current_candle["rsi"]
            if side == "long" and rsi < 20:
                position_multiplier *= self.stake_rsi_low_multiplier.value

        final_stake = base_stake * position_multiplier
        return max(min_stake, min(final_stake, max_stake))
