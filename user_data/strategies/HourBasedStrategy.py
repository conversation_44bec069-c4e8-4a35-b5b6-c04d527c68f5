from freqtrade.strategy import IntParameter, IStrategy
from pandas import DataFrame


class HourBasedStrategy_5m(IStrategy):
    """
    基于时间的交易策略 - 5分钟时间框架版本

    这是一个基于特定时间段进行交易的策略，通过设定买入和卖出的时间窗口来控制交易时机。
    策略的核心思想是在特定的分钟区间内进行买入和卖出操作，适用于有明确时间规律的市场。

    主要特点：
    1. 基于分钟级别的时间控制
    2. 可通过超参数优化调整最佳交易时间窗口
    3. 简单直接的时间驱动策略
    4. 适合短期高频交易

    作者: FreqTrade社区
    时间框架: 5分钟
    """

    # 买入超参数配置
    # 定义最优的买入时间窗口
    buy_params = {
        "buy_hour_max": 24,  # 买入时间窗口结束分钟数
        "buy_hour_min": 4,  # 买入时间窗口开始分钟数
    }

    # 卖出超参数配置
    # 定义最优的卖出时间窗口
    sell_params = {
        "sell_hour_max": 21,  # 卖出时间窗口结束分钟数
        "sell_hour_min": 22,  # 卖出时间窗口开始分钟数
    }

    # ROI收益率表：定义不同时间点的最小收益率要求
    # 格式：{"时间(分钟)": 收益率}
    # 0分钟时要求52.8%收益率，169分钟后降至11.3%，以此类推
    minimal_roi = {"0": 0.528, "169": 0.113, "528": 0.089, "1837": 0}

    # 止损设置：最大允许亏损10%
    stoploss = -0.10

    # 最优时间框架：5分钟K线
    timeframe = "5m"

    # 买入时间窗口超参数
    # 范围：0-1440分钟（一天24小时），用于超参数优化
    buy_hour_min = IntParameter(0, 1440, default=1, space="buy")  # 买入开始时间（分钟）
    buy_hour_max = IntParameter(0, 1440, default=0, space="buy")  # 买入结束时间（分钟）

    # 卖出时间窗口超参数
    # 范围：0-1440分钟（一天24小时），用于超参数优化
    sell_hour_min = IntParameter(0, 1440, default=1, space="sell")  # 卖出开始时间（分钟）
    sell_hour_max = IntParameter(0, 1440, default=0, space="sell")  # 卖出结束时间（分钟）

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        为策略计算所需的技术指标。这个策略只需要时间信息，
        因此只提取每根K线的分钟数用于时间窗口判断。

        Args:
            dataframe: OHLCV价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了技术指标的数据框
        """
        # 提取每根K线的分钟数（0-59分钟）
        # 用于判断当前时间是否在设定的交易时间窗口内
        dataframe["minute"] = dataframe["date"].dt.minute
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充买入信号

        基于时间窗口生成买入信号。当当前分钟数在设定的买入时间窗口内时，
        生成买入信号。

        买入逻辑：
        - 当前分钟数在buy_hour_min和buy_hour_max之间时买入

        Args:
            dataframe: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了买入信号的数据框
        """
        # 时间窗口买入条件
        # 当当前分钟数在设定的买入时间窗口内时，设置买入信号
        dataframe.loc[
            (dataframe["minute"].between(self.buy_hour_min.value, self.buy_hour_max.value)),
            "enter_long",  # 设置多头入场信号
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充卖出信号

        基于时间窗口生成卖出信号。当当前分钟数在设定的卖出时间窗口内时，
        生成卖出信号。

        卖出逻辑：
        - 当前分钟数在sell_hour_min和sell_hour_max之间时卖出

        Args:
            dataframe: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了卖出信号的数据框
        """
        # 时间窗口卖出条件
        # 当当前分钟数在设定的卖出时间窗口内时，设置卖出信号
        dataframe.loc[
            (dataframe["minute"].between(self.sell_hour_min.value, self.sell_hour_max.value)),
            "exit_long",  # 设置多头出场信号
        ] = 1
        return dataframe

    def leverage(
        self,
        pair: str,
        current_time,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        从配置文件中读取杠杆设置，如果配置文件中没有设置则使用默认值1.0

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", 1.0)

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(config_leverage, max_leverage))

        return leverage
