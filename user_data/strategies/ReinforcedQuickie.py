# --- Do not remove these libs ---
from datetime import datetime

import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy  # noqa
import numpy as np
import talib.abstract as ta
from freqtrade.strategy import CategoricalParameter, DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame, DatetimeIndex, Series, merge


# 自定义技术指标函数
# ##################################################################################################
def RMI(dataframe, *, length=20, mom=5):
    """
    相对动量指标 (Relative Momentum Index)

    RMI是RSI的改进版本，使用动量而不是简单的价格变化来计算。
    它通过比较N期前的价格来计算动量，提供更平滑的信号。

    参数:
        dataframe: 价格数据框
        length: EMA计算周期，默认20
        mom: 动量计算周期，默认5

    返回:
        Series: RMI指标值（0-100）

    来源: https://github.com/freqtrade/technical/blob/master/technical/indicators/indicators.py#L912
    """
    df = dataframe.copy()

    # 计算上涨动量：当前收盘价与N期前收盘价的差值（仅保留正值）
    df["maxup"] = (df["close"] - df["close"].shift(mom)).clip(lower=0)
    # 计算下跌动量：N期前收盘价与当前收盘价的差值（仅保留正值）
    df["maxdown"] = (df["close"].shift(mom) - df["close"]).clip(lower=0)

    # 填充NaN值为0
    df.fillna(0, inplace=True)

    # 计算上涨动量的指数移动平均
    df["emaInc"] = ta.EMA(df, price="maxup", timeperiod=length)
    # 计算下跌动量的指数移动平均
    df["emaDec"] = ta.EMA(df, price="maxdown", timeperiod=length)

    # 计算RMI值：类似RSI的计算方式，但使用动量EMA
    df["RMI"] = np.where(df["emaDec"] == 0, 0, 100 - 100 / (1 + df["emaInc"] / df["emaDec"]))

    return df["RMI"]


def zema(dataframe, period, field="close"):
    """
    零滞后指数移动平均线 (Zero Lag Exponential Moving Average)

    ZEMA是EMA的改进版本，通过减少滞后性来提供更及时的信号。
    它使用双重EMA计算来减少传统EMA的滞后问题。

    参数:
        dataframe: 价格数据框
        period: 计算周期
        field: 计算字段，默认为"close"

    返回:
        Series: ZEMA指标值

    来源: https://github.com/freqtrade/technical/blob/master/technical/indicators/overlap_studies.py#L79
    修改: 使用ta.EMA替代technical库的ema
    """
    df = dataframe.copy()

    # 第一次EMA计算
    df["ema1"] = ta.EMA(df[field], timeperiod=period)
    # 第二次EMA计算（对第一次EMA结果再次计算EMA）
    df["ema2"] = ta.EMA(df["ema1"], timeperiod=period)
    # 计算差值：用于减少滞后
    df["d"] = df["ema1"] - df["ema2"]
    # 零滞后EMA：第一次EMA加上差值
    df["zema"] = df["ema1"] + df["d"]

    return df["zema"]


def same_length(bigger, shorter):
    """
    数组长度对齐函数

    将较短的数组前面填充NaN值，使其与较长数组长度一致。

    参数:
        bigger: 较长的数组
        shorter: 较短的数组

    返回:
        numpy.array: 长度对齐后的数组
    """
    return np.concatenate((np.full((bigger.shape[0] - shorter.shape[0]), np.nan), shorter))


def mastreak(dataframe: DataFrame, period: int = 4, field="close") -> Series:
    """
    移动平均连续趋势指标 (MA Streak)

    计算移动平均线连续上涨或下跌的次数，用于识别趋势的强度和持续性。
    正值表示连续上涨次数，负值表示连续下跌次数。

    参数:
        dataframe: 价格数据框
        period: ZEMA计算周期，默认4
        field: 计算字段，默认"close"

    返回:
        Series: 趋势连续次数（正数为上涨，负数为下跌）

    来源: https://www.tradingview.com/script/Yq1z7cIv-MA-Streak-Can-Show-When-a-Run-Is-Getting-Long-in-the-Tooth/
    """
    df = dataframe.copy()

    # 使用ZEMA计算平均值
    avgval = zema(df, period, field)

    # 计算价格变化方向
    arr = np.diff(avgval)
    # 计算正向变化的累积计数
    pos = np.clip(arr, 0, 1).astype(bool).cumsum()
    # 计算负向变化的累积计数
    neg = np.clip(arr, -1, 0).astype(bool).cumsum()

    # 计算连续趋势次数
    streak = np.where(
        arr >= 0,  # 如果是上涨
        pos - np.maximum.accumulate(np.where(arr <= 0, pos, 0)),  # 计算连续上涨次数
        -neg + np.maximum.accumulate(np.where(arr >= 0, neg, 0)),  # 计算连续下跌次数
    )

    # 对齐数组长度
    res = same_length(df["close"], streak)

    return res


def linear_growth(
    start: float, end: float, start_time: int, end_time: int, trade_time: int
) -> float:
    """
    线性增长函数

    在指定时间段内从起始值线性增长到结束值。
    用于动态调整ROI等参数，实现时间相关的参数变化。

    参数:
        start: 起始值
        end: 结束值
        start_time: 开始时间（分钟）
        end_time: 结束时间（分钟）
        trade_time: 当前交易时间（分钟）

    返回:
        float: 当前时间对应的值
    """
    # 计算实际经过的时间
    time = max(0, trade_time - start_time)
    # 计算增长率
    rate = (end - start) / (end_time - start_time)

    # 返回当前时间对应的值，不超过结束值
    return min(end, start + (rate * time))


def pcc(dataframe: DataFrame, period: int = 20, mult: int = 2):
    """
    百分比变化通道 (Percent Change Channel)

    PCC类似于Keltner通道，但使用价格的百分比变化来设置通道距离。
    通过分析价格变化的百分比来构建动态通道，更好地适应不同价格水平的资产。

    参数:
        dataframe: 价格数据框
        period: 计算周期，默认20
        mult: 倍数因子，默认2

    返回:
        tuple: (上轨, 范围均值, 下轨)

    来源: https://www.tradingview.com/script/6wwAWXA1-MA-Streak-Change-Channel/
    """
    df = dataframe.copy()

    # 获取前一根K线的收盘价
    df["previous_close"] = df["close"].shift()

    # 计算收盘价变化百分比
    df["close_change"] = (df["close"] - df["previous_close"]) / df["previous_close"] * 100
    # 计算最高价相对收盘价的变化百分比
    df["high_change"] = (df["high"] - df["close"]) / df["close"] * 100
    # 计算最低价相对收盘价的变化百分比
    df["low_change"] = (df["low"] - df["close"]) / df["close"] * 100

    # 计算价格波动范围
    df["delta"] = df["high_change"] - df["low_change"]

    # 使用ZEMA计算中线和范围均值
    mid = zema(df, period, "close_change")  # 价格变化的零滞后均线
    rangema = zema(df, period, "delta")  # 波动范围的零滞后均线

    # 计算上下轨
    upper = mid + rangema * mult  # 上轨
    lower = mid - rangema * mult  # 下轨

    return upper, rangema, lower


def SSLChannels_ATR(dataframe, length=7):
    """
    SSL通道与ATR指标 (SSL Channels with ATR)

    SSL通道是一种趋势跟踪指标，结合ATR来动态调整通道宽度。
    它通过比较收盘价与动态通道来确定趋势方向。

    参数:
        dataframe: 价格数据框
        length: SMA计算周期，默认7

    返回:
        tuple: (SSL下轨, SSL上轨)

    来源: https://www.tradingview.com/script/SKHqWzql-SSL-ATR-channel/
    Python实现: @JimmyNixx
    """
    df = dataframe.copy()

    # 计算ATR（平均真实波幅）
    df["ATR"] = ta.ATR(df, timeperiod=14)
    # 计算高点SMA并加上ATR
    df["smaHigh"] = df["high"].rolling(length).mean() + df["ATR"]
    # 计算低点SMA并减去ATR
    df["smaLow"] = df["low"].rolling(length).mean() - df["ATR"]

    # 确定价格位置：高于上轨为1，低于下轨为-1，中间为NaN
    df["hlv"] = np.where(
        df["close"] > df["smaHigh"], 1, np.where(df["close"] < df["smaLow"], -1, np.nan)
    )
    # 前向填充NaN值
    df["hlv"] = df["hlv"].ffill()

    # 根据价格位置确定SSL通道
    df["sslDown"] = np.where(df["hlv"] < 0, df["smaHigh"], df["smaLow"])  # SSL下轨
    df["sslUp"] = np.where(df["hlv"] < 0, df["smaLow"], df["smaHigh"])  # SSL上轨

    return df["sslDown"], df["sslUp"]


def SROC(dataframe, roclen=21, emalen=13, smooth=21):
    """
    平滑变化率指标 (Smoothed Rate of Change)

    SROC是ROC指标的平滑版本，通过对EMA应用ROC来减少噪音。
    用于识别价格动量的变化趋势。

    参数:
        dataframe: 价格数据框
        roclen: ROC计算周期，默认21
        emalen: EMA计算周期，默认13
        smooth: 平滑周期，默认21

    返回:
        Series: SROC指标值
    """
    df = dataframe.copy()

    # 计算价格变化率
    roc = ta.ROC(df, timeperiod=roclen)
    # 计算指数移动平均
    ema = ta.EMA(df, timeperiod=emalen)
    # 对EMA应用ROC得到平滑的变化率
    sroc = ta.ROC(ema, timeperiod=smooth)

    return sroc


def linear_decay(
    start: float, end: float, start_time: int, end_time: int, trade_time: int
) -> float:
    """
    线性衰减函数

    在指定时间段内从起始值线性衰减到结束值。
    用于动态调整止损等参数，实现时间相关的参数衰减。

    参数:
        start: 起始值
        end: 结束值
        start_time: 开始时间（分钟）
        end_time: 结束时间（分钟）
        trade_time: 当前交易时间（分钟）

    返回:
        float: 当前时间对应的值
    """
    # 计算实际经过的时间
    time = max(0, trade_time - start_time)
    # 计算衰减率
    rate = (start - end) / (end_time - start_time)

    # 返回当前时间对应的值，不低于结束值
    return max(end, start - (rate * time))


# #####################################################################################################


class ReinforcedQuickie(IStrategy):
    """
    强化快速交易策略 (Reinforced Quickie Strategy)

    这是一个基于趋势强化的快速交易策略，专注于在上升趋势市场中寻找买入机会。
    策略结合多种技术指标和自定义退出逻辑，实现动态的风险管理。

    核心理念：
    - 只在上升趋势市场中买入
    - 使用重采样技术确认整体趋势方向
    - 结合多种技术指标确认买入时机
    - 实现复杂的自定义退出和止损逻辑

    主要特点：
    1. 趋势强化：通过重采样确认长期趋势
    2. 多指标确认：EMA、布林带、RSI、CCI、MFI等
    3. 自定义止损：基于时间和ROC的动态止损
    4. 自定义退出：基于趋势和ROI的智能退出
    5. 杠杆优化：支持杠杆参数优化

    作者: Gert Wohlgemuth
    时间框架: 5分钟
    适用市场: 上升趋势市场
    """

    # 最小ROI设置：策略设计的最小收益率
    # 如果配置文件包含"minimal_roi"，此属性将被覆盖
    minimal_roi = {"0": 100}  # 设置为100%，实际使用自定义退出逻辑

    # 最优止损设置：策略设计的止损点
    # 如果配置文件包含"stoploss"，此属性将被覆盖
    stoploss = -0.99  # 设置为-99%，实际使用自定义止损逻辑

    # 策略最优时间框架
    timeframe = "5m"

    # 只处理新K线，提高性能
    process_only_new_candles = True
    # 启动时需要的K线数量
    startup_candle_count = 30

    # 启用自定义止损
    use_custom_stoploss = True

    # 自定义交易信息存储
    custom_trade_info = {}

    # 订单类型配置
    order_types = {
        "entry": "market",  # 入场订单：市价单
        "exit": "market",  # 出场订单：市价单
        "emergency_exit": "market",  # 紧急退出：市价单
        "force_entry": "market",  # 强制入场：市价单
        "force_exit": "market",  # 强制退出：市价单
        "stoploss": "market",  # 止损订单：市价单
        "stoploss_on_exchange": False,  # 不在交易所设置止损
        "stoploss_on_exchange_interval": 60,  # 止损检查间隔
        "stoploss_on_exchange_limit_ratio": 0.99,  # 止损限价比例
    }

    # 重采样因子：用于确定总体趋势，基本上不在趋势中时不买入
    # 通过重采样更大的时间框架来确认长期趋势方向
    buy_op = True  # 买入参数优化开关
    resample_factor = IntParameter(low=1, high=100, default=25, space="buy", optimize=buy_op)

    # 杠杆优化参数
    leverage_optimize = True  # 杠杆优化开关
    leverage_num = IntParameter(low=1, high=10, default=1, space="buy", optimize=leverage_optimize)

    # 自定义退出参数
    ce_op = True  # 自定义退出优化开关

    # 回撤退出金额：当利润回撤超过此值时触发退出
    csell_pullback_amount = DecimalParameter(
        0.005, 0.15, default=0.01, space="sell", load=True, optimize=ce_op
    )

    # ROI类型：静态、衰减或阶梯式ROI
    csell_roi_type = CategoricalParameter(
        ["static", "decay", "step"], default="step", space="sell", load=True, optimize=ce_op
    )

    # ROI起始值：初始最小收益率要求
    csell_roi_start = DecimalParameter(
        0.01, 0.15, default=0.01, space="sell", load=True, optimize=ce_op
    )

    # ROI结束值：最终最小收益率要求
    csell_roi_end = DecimalParameter(0.0, 0.01, default=0, space="sell", load=True, optimize=ce_op)

    # ROI时间：ROI变化的时间点（分钟）
    csell_roi_time = IntParameter(720, 1440, default=720, space="sell", load=True, optimize=ce_op)

    # 趋势类型：用于判断趋势的指标类型
    csell_trend_type = CategoricalParameter(
        ["rmi", "ssl", "candle", "any", "none"],
        default="any",
        space="sell",
        load=True,
        optimize=ce_op,
    )

    # 是否启用回撤退出
    csell_pullback = CategoricalParameter(
        [True, False], default=True, space="sell", load=True, optimize=ce_op
    )

    # 回撤退出是否尊重ROI
    csell_pullback_respect_roi = CategoricalParameter(
        [True, False], default=False, space="sell", load=True, optimize=ce_op
    )

    # 趋势结束退出是否尊重ROI
    csell_endtrend_respect_roi = CategoricalParameter(
        [True, False], default=False, space="sell", load=True, optimize=ce_op
    )

    # 自定义止损参数
    cs_op = True  # 自定义止损优化开关

    # 止损触发阈值：当亏损达到此值时开始考虑止损
    cstop_loss_threshold = DecimalParameter(
        -0.35, -0.01, default=-0.03, space="sell", load=True, optimize=cs_op
    )

    # 止损退出方式：基于ROC、时间、任意条件或不退出
    cstop_bail_how = CategoricalParameter(
        ["roc", "time", "any", "none"], default="none", space="sell", load=True, optimize=cs_op
    )

    # 止损ROC阈值：当SROC低于此值时触发止损
    cstop_bail_roc = DecimalParameter(
        -5.0, -1.0, default=-3.0, space="sell", load=True, optimize=cs_op
    )

    # 止损时间阈值：持仓超过此时间（分钟）时考虑止损
    cstop_bail_time = IntParameter(60, 1440, default=720, space="sell", load=True, optimize=cs_op)

    # 时间止损是否考虑趋势：在趋势中时是否延迟时间止损
    cstop_bail_time_trend = CategoricalParameter(
        [True, False], default=True, space="sell", load=True, optimize=cs_op
    )

    # 最大止损：绝对最大亏损限制
    cstop_max_stoploss = DecimalParameter(
        -0.30, -0.01, default=-0.10, space="sell", load=True, optimize=cs_op
    )

    # EMA周期常量定义
    EMA_SHORT_TERM = 5  # 短期EMA周期
    EMA_MEDIUM_TERM = 12  # 中期EMA周期
    EMA_LONG_TERM = 21  # 长期EMA周期

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        为策略计算所需的各种技术指标，包括趋势指标、动量指标、波动性指标等。
        同时初始化自定义交易信息存储，用于跟踪交易状态。

        Args:
            dataframe: OHLCV价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了所有技术指标的数据框
        """
        # 初始化自定义交易信息存储
        if metadata["pair"] not in self.custom_trade_info:
            self.custom_trade_info[metadata["pair"]] = {}
            if "had-trend" not in self.custom_trade_info[metadata["pair"]]:
                self.custom_trade_info[metadata["pair"]]["had-trend"] = False

        # 重采样数据以确定总体趋势
        dataframe = self.resample(dataframe, self.timeframe, self.resample_factor.value)

        ##################################################################################
        # 买入和卖出指标计算

        # EMA指标：不同周期的指数移动平均线
        dataframe["ema_{}".format(self.EMA_SHORT_TERM)] = ta.EMA(
            dataframe, timeperiod=self.EMA_SHORT_TERM
        )  # 短期EMA(5)
        dataframe["ema_{}".format(self.EMA_MEDIUM_TERM)] = ta.EMA(
            dataframe, timeperiod=self.EMA_MEDIUM_TERM
        )  # 中期EMA(12)
        dataframe["ema_{}".format(self.EMA_LONG_TERM)] = ta.EMA(
            dataframe, timeperiod=self.EMA_LONG_TERM
        )  # 长期EMA(21)

        # 布林带指标：基于典型价格计算
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe["bb_lowerband"] = bollinger["lower"]  # 布林带下轨
        dataframe["bb_middleband"] = bollinger["mid"]  # 布林带中轨
        dataframe["bb_upperband"] = bollinger["upper"]  # 布林带上轨

        # 价格极值：中期周期内的最高价和最低价
        dataframe["min"] = ta.MIN(dataframe, timeperiod=self.EMA_MEDIUM_TERM)  # 12期最低价
        dataframe["max"] = ta.MAX(dataframe, timeperiod=self.EMA_MEDIUM_TERM)  # 12期最高价

        # 动量和强度指标
        dataframe["cci"] = ta.CCI(dataframe)  # 商品通道指数
        dataframe["mfi"] = ta.MFI(dataframe)  # 资金流量指数
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=7)  # 7期相对强弱指数

        # 平均价格：OHLC四价平均
        dataframe["average"] = (
            dataframe["close"] + dataframe["open"] + dataframe["high"] + dataframe["low"]
        ) / 4

        # RMI指标及其趋势判断
        dataframe["rmi"] = RMI(dataframe, length=24, mom=5)  # 24期RMI，5期动量
        # RMI上升信号：当前RMI >= 前一期RMI
        dataframe["rmi-up"] = np.where(dataframe["rmi"] >= dataframe["rmi"].shift(), 1, 0)
        # RMI上升趋势：5期内至少3次上升
        dataframe["rmi-up-trend"] = np.where(dataframe["rmi-up"].rolling(5).sum() >= 3, 1, 0)

        # 仅用于ROI和自定义止损的指标
        ssldown, sslup = SSLChannels_ATR(dataframe, length=21)  # SSL通道
        dataframe["sroc"] = SROC(dataframe, roclen=21, emalen=13, smooth=21)  # 平滑ROC
        # SSL方向：上轨>下轨为上升，否则为下降
        dataframe["ssl-dir"] = np.where(sslup > ssldown, "up", "down")

        # 趋势、峰值和交叉信号
        # 阳线信号：收盘价 >= 开盘价
        dataframe["candle-up"] = np.where(dataframe["close"] >= dataframe["open"], 1, 0)
        # 阳线趋势：5期内至少3根阳线
        dataframe["candle-up-trend"] = np.where(dataframe["candle-up"].rolling(5).sum() >= 3, 1, 0)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充买入信号

        基于技术指标生成买入信号。策略使用两种主要的买入模式：
        1. 超卖反弹：价格低于EMA且触及布林带下轨
        2. V型底部：识别特定的V型底部形态

        买入逻辑：
        - 模式1：价格低于短期和中期EMA，且等于最低价，且低于布林带下轨
        - 模式2：V型底部形态（左侧偏斜以增加反应性）+ 多个超卖指标确认
        - 安全保护：避免下跌趋势市场和拉高出货

        Args:
            dataframe: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了买入信号的数据框
        """
        dataframe.loc[
            (
                (
                    # 买入模式1：超卖反弹
                    (
                        (
                            dataframe["close"] < dataframe["ema_{}".format(self.EMA_SHORT_TERM)]
                        )  # 价格低于短期EMA
                        & (
                            dataframe["close"] < dataframe["ema_{}".format(self.EMA_MEDIUM_TERM)]
                        )  # 价格低于中期EMA
                        & (dataframe["close"] == dataframe["min"])  # 价格等于最低价
                        & (dataframe["close"] <= dataframe["bb_lowerband"])  # 价格低于布林带下轨
                    )
                    |
                    # 买入模式2：简单V型底部形态（左侧偏斜以增加反应性）
                    # 必须在非常慢的平均线下方
                    # 这种模式只捕获少数，但通常是非常好的买点
                    (
                        (
                            dataframe["average"].shift(5) > dataframe["average"].shift(4)
                        )  # 5期前 > 4期前
                        & (
                            dataframe["average"].shift(4) > dataframe["average"].shift(3)
                        )  # 4期前 > 3期前
                        & (
                            dataframe["average"].shift(3) > dataframe["average"].shift(2)
                        )  # 3期前 > 2期前
                        & (
                            dataframe["average"].shift(2) > dataframe["average"].shift(1)
                        )  # 2期前 > 1期前
                        & (
                            dataframe["average"].shift(1) < dataframe["average"].shift(0)
                        )  # 1期前 < 当前（形成V底）
                        & (
                            dataframe["low"].shift(1) < dataframe["bb_middleband"]
                        )  # 前期低点低于布林带中轨
                        & (dataframe["cci"].shift(1) < -100)  # 前期CCI超卖（<-100）
                        & (dataframe["rsi"].shift(1) < 30)  # 前期RSI超卖（<30）
                        & (dataframe["mfi"].shift(1) < 30)  # 前期MFI超卖（<30）
                    )
                )
                # 防范下跌趋势市场和拉高出货的安全保护
                & (
                    # 成交量不能过大（避免拉高出货）
                    (
                        dataframe["volume"]
                        < (dataframe["volume"].rolling(window=30).mean().shift(1) * 20)
                    )
                    & (
                        dataframe["resample_sma"] < dataframe["close"]
                    )  # 重采样SMA低于当前价格（确认上升趋势）
                    & (
                        dataframe["resample_sma"].shift(1) < dataframe["resample_sma"]
                    )  # 重采样SMA呈上升趋势
                )
            ),
            "enter_long",  # 设置多头入场信号
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充卖出信号

        基于技术指标生成卖出信号。策略使用两种主要的卖出模式：
        1. 超买卖出：价格高于EMA且触及布林带上轨，MFI超买
        2. 连续阳线卖出：连续8根阳线且RSI超买

        卖出逻辑：
        - 模式1：价格高于短期和中期EMA，且等于最高价，且高于布林带上轨，且MFI>80
        - 模式2：连续8根阳线（开盘价<收盘价）且RSI>70

        注意：此方法设置exit_long=0，实际策略主要依赖自定义退出逻辑

        Args:
            dataframe: 包含技术指标的价格数据框
            metadata: 交易对元数据信息

        Returns:
            DataFrame: 添加了卖出信号的数据框
        """
        dataframe.loc[
            (
                # 卖出模式1：超买卖出
                (
                    dataframe["close"] > dataframe["ema_{}".format(self.EMA_SHORT_TERM)]
                )  # 价格高于短期EMA
                & (
                    dataframe["close"] > dataframe["ema_{}".format(self.EMA_MEDIUM_TERM)]
                )  # 价格高于中期EMA
                & (dataframe["close"] >= dataframe["max"])  # 价格等于最高价
                & (dataframe["close"] >= dataframe["bb_upperband"])  # 价格高于布林带上轨
                & (dataframe["mfi"] > 80)  # MFI超买（>80）
            )
            |
            # 卖出模式2：连续8根阳线卖出
            # 在高RSI时总是卖出连续8根阳线
            (
                (dataframe["open"] < dataframe["close"])  # 当前K线为阳线
                & (dataframe["open"].shift(1) < dataframe["close"].shift(1))  # 前1根为阳线
                & (dataframe["open"].shift(2) < dataframe["close"].shift(2))  # 前2根为阳线
                & (dataframe["open"].shift(3) < dataframe["close"].shift(3))  # 前3根为阳线
                & (dataframe["open"].shift(4) < dataframe["close"].shift(4))  # 前4根为阳线
                & (dataframe["open"].shift(5) < dataframe["close"].shift(5))  # 前5根为阳线
                & (dataframe["open"].shift(6) < dataframe["close"].shift(6))  # 前6根为阳线
                & (dataframe["open"].shift(7) < dataframe["close"].shift(7))  # 前7根为阳线
                & (dataframe["rsi"] > 70)  # RSI超买（>70）
            ),
            "exit_long",  # 设置多头出场信号
        ] = 0  # 注意：设置为0，实际主要使用自定义退出逻辑
        return dataframe

    def resample(self, dataframe, interval, factor):
        """
        重采样数据以确定趋势强化逻辑

        通过重采样到更大的时间框架来确定是否处于上升趋势、下降趋势或横盘趋势。
        这是策略的核心强化逻辑，用于过滤不利的市场环境。

        处理步骤：
        1. 将数据重采样到更大的时间框架（interval * factor）
        2. 计算重采样数据的25期SMA
        3. 将结果插值回原始时间框架
        4. 合并到原始数据框中

        Args:
            dataframe: 原始价格数据框
            interval: 原始时间间隔（如"5m"）
            factor: 重采样因子（如25，将5分钟重采样为125分钟）

        Returns:
            DataFrame: 添加了重采样SMA的数据框
        """
        df = dataframe.copy()
        # 设置日期为索引
        df = df.set_index(DatetimeIndex(df["date"]))

        # 定义OHLC聚合字典
        ohlc_dict = {"open": "first", "high": "max", "low": "min", "close": "last"}

        # 重采样到更大的时间框架
        df = (
            df.resample(str(int(interval[:-1]) * factor) + "min", label="right")
            .agg(ohlc_dict)
            .dropna(how="any")
        )

        # 计算重采样数据的25期简单移动平均
        df["resample_sma"] = ta.SMA(df, timeperiod=25, price="close")

        # 删除OHLC列，只保留SMA
        df = df.drop(columns=["open", "high", "low", "close"])

        # 重新采样回原始时间框架
        df = df.resample(interval[:-1] + "min")

        # 使用时间插值填充缺失值
        df = df.interpolate(method="time")

        # 重置索引
        df["date"] = df.index
        df.index = range(len(df))

        # 合并到原始数据框
        dataframe = merge(dataframe, df, on="date", how="left")
        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        优先从配置文件中读取杠杆设置，如果配置文件中没有设置则使用策略内部的超参数

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 优先从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", None)

        if config_leverage is not None:
            # 使用配置文件中的杠杆设置
            leverage = config_leverage
        else:
            # 如果配置文件中没有设置，使用策略内部的超参数
            leverage = self.leverage_num.value

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(leverage, max_leverage))

        return leverage

    """
    自定义止损逻辑
    """

    def custom_stoploss(
        self,
        pair: str,
        trade: "Trade",
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ) -> float:
        """
        自定义止损函数

        实现动态止损逻辑，基于亏损程度、时间和市场条件来决定是否止损。
        提供多种止损触发条件：最大止损、ROC止损、时间止损。

        止损逻辑：
        1. 绝对止损：亏损超过最大止损限制时立即止损
        2. ROC止损：在亏损状态下，如果SROC指标恶化则止损
        3. 时间止损：持仓时间过长时止损，但在趋势中可能延迟

        Args:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈亏比例

        Returns:
            float: 止损比例（0.01表示立即止损，1表示不止损）
        """
        # 获取最新的技术指标数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        # 计算交易持续时间（分钟）
        trade_dur = int((current_time.timestamp() - trade.open_date_utc.timestamp()) // 60)

        # 获取趋势状态
        in_trend = self.custom_trade_info[trade.pair]["had-trend"]

        # 绝对最大止损：如果亏损超过最大止损限制，立即止损
        if current_profit < self.cstop_max_stoploss.value:
            return 0.01

        # 当处于亏损状态时的止损判断
        if current_profit < self.cstop_loss_threshold.value:
            # ROC止损：基于变化率的动态止损
            if self.cstop_bail_how.value == "roc" or self.cstop_bail_how.value == "any":
                # 如果SROC指标低于阈值，表明趋势恶化，执行止损
                if last_candle["sroc"] <= self.cstop_bail_roc.value:
                    return 0.01

            # 时间止损：基于持仓时间的止损
            if self.cstop_bail_how.value == "time" or self.cstop_bail_how.value == "any":
                # 如果持仓时间超过设定阈值
                if trade_dur > self.cstop_bail_time.value:
                    # 如果启用趋势考虑且当前在趋势中，延迟止损
                    if self.cstop_bail_time_trend.value and in_trend:
                        return 1  # 不止损，等待趋势结束
                    else:
                        return 0.01  # 执行时间止损

        # 默认不止损
        return 1

    """
    自定义退出逻辑
    """

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        自定义退出函数

        实现智能退出逻辑，基于趋势状态、ROI要求和回撤控制来决定退出时机。
        提供多种退出策略：趋势跟踪、回撤退出、ROI退出。

        退出逻辑：
        1. 趋势中持仓：在上升趋势中尽量持仓，除非发生显著回撤
        2. 回撤退出：当利润从最高点回撤超过设定阈值时退出
        3. ROI退出：当利润达到最小ROI要求时退出
        4. 趋势结束退出：当趋势结束时根据ROI情况决定是否退出

        Args:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈亏比例

        Returns:
            str or None: 退出标签（用于识别退出原因）或None（不退出）
        """
        # 获取最新的技术指标数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        # 计算交易相关数据
        trade_dur = int(
            (current_time.timestamp() - trade.open_date_utc.timestamp()) // 60
        )  # 交易持续时间
        max_profit = max(0, trade.calc_profit_ratio(trade.max_rate))  # 历史最大利润
        pullback_value = max(0, (max_profit - self.csell_pullback_amount.value))  # 回撤阈值
        in_trend = False  # 趋势状态标志

        # 根据设定的ROI类型确定当前最小ROI要求
        if self.csell_roi_type.value == "static":
            # 静态ROI：始终使用起始ROI值
            min_roi = self.csell_roi_start.value
        elif self.csell_roi_type.value == "decay":
            # 衰减ROI：随时间从起始值衰减到结束值
            min_roi = linear_decay(
                self.csell_roi_start.value,
                self.csell_roi_end.value,
                0,
                self.csell_roi_time.value,
                trade_dur,
            )
        elif self.csell_roi_type.value == "step":
            # 阶梯ROI：在指定时间前使用起始值，之后使用结束值
            if trade_dur < self.csell_roi_time.value:
                min_roi = self.csell_roi_start.value
            else:
                min_roi = self.csell_roi_end.value

        # 判断当前是否处于上升趋势
        if self.csell_trend_type.value == "rmi" or self.csell_trend_type.value == "any":
            # 基于RMI指标判断趋势
            if last_candle["rmi-up-trend"] == 1:
                in_trend = True
        if self.csell_trend_type.value == "ssl" or self.csell_trend_type.value == "any":
            # 基于SSL通道判断趋势
            if last_candle["ssl-dir"] == "up":
                in_trend = True
        if self.csell_trend_type.value == "candle" or self.csell_trend_type.value == "any":
            # 基于K线形态判断趋势
            if last_candle["candle-up-trend"] == 1:
                in_trend = True

        # 趋势中的退出逻辑：在趋势中除非达到回撤阈值否则不退出
        if in_trend and current_profit > 0:
            # 记录该交易对曾经处于趋势中，用于后续更有用的退出信息
            self.custom_trade_info[trade.pair]["had-trend"] = True

            # 如果启用回撤退出且利润已回撤到阈值，考虑退出
            if self.csell_pullback.value and (current_profit <= pullback_value):
                if self.csell_pullback_respect_roi.value and current_profit > min_roi:
                    return "intrend_pullback_roi"  # 趋势中回撤退出（满足ROI）
                elif not self.csell_pullback_respect_roi.value:
                    if current_profit > min_roi:
                        return "intrend_pullback_roi"  # 趋势中回撤退出（满足ROI）
                    else:
                        return "intrend_pullback_noroi"  # 趋势中回撤退出（不满足ROI）

            # 在趋势中且回撤未达到阈值或未启用回撤退出，继续持仓
            return None

        # 非趋势状态的退出逻辑：主要基于ROI值
        elif not in_trend:
            # 如果之前曾经在趋势中
            if self.custom_trade_info[trade.pair]["had-trend"]:
                if current_profit > min_roi:
                    self.custom_trade_info[trade.pair]["had-trend"] = False
                    return "trend_roi"  # 趋势结束退出（满足ROI）
                elif not self.csell_endtrend_respect_roi.value:
                    self.custom_trade_info[trade.pair]["had-trend"] = False
                    return "trend_noroi"  # 趋势结束退出（不满足ROI）
            # 从未在趋势中，直接基于ROI判断
            elif current_profit > min_roi:
                return "notrend_roi"  # 非趋势ROI退出
        else:
            # 其他情况不退出
            return None
