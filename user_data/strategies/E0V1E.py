"""
E0V1E 交易策略

基于RSI和多种技术指标的量化交易策略
主要特点：
1. 使用RSI快慢线组合判断入场时机
2. 结合SMA、CTI指标进行多重过滤
3. 自定义退出逻辑，包含盈利退出和止损退出
4. 支持杠杆交易，从配置文件读取杠杆设置
5. 包含冷却期保护机制

策略逻辑：
- 买入条件：RSI慢线下降 + RSI快线低位 + 价格低于SMA + CTI过滤
- 卖出条件：基于STOCH和CCI指标的自定义退出逻辑
- 风险控制：25%止损 + 追踪止损 + 冷却期保护
"""

import warnings
from datetime import datetime
from functools import reduce

import pandas_ta as pta
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame

# 忽略运行时警告
warnings.simplefilter(action="ignore", category=RuntimeWarning)


class E0V1E(IStrategy):
    """
    E0V1E策略类

    基于RSI多时间框架分析的交易策略，结合SMA和CTI指标进行信号过滤
    """

    # ===== 策略基础配置 =====
    minimal_roi = {"0": 1}  # 最小收益率：不设置ROI退出，完全依赖自定义退出逻辑
    timeframe = "5m"  # 时间框架：5分钟K线
    process_only_new_candles = True  # 只处理新K线，避免重复计算
    startup_candle_count = 20  # 启动时需要的历史K线数量

    # ===== 订单类型配置 =====
    order_types = {
        "entry": "market",  # 入场订单：市价单
        "exit": "market",  # 出场订单：市价单
        "emergency_exit": "market",  # 紧急退出：市价单
        "force_entry": "market",  # 强制入场：市价单
        "force_exit": "market",  # 强制退出：市价单
        "stoploss": "market",  # 止损订单：市价单
        "stoploss_on_exchange": False,  # 不在交易所设置止损
        "stoploss_on_exchange_interval": 60,  # 交易所止损检查间隔
        "stoploss_on_exchange_market_ratio": 0.99,  # 交易所止损市价比率
    }

    # ===== 风险控制配置 =====
    stoploss = -0.25  # 固定止损：25%
    trailing_stop = True  # 启用追踪止损
    trailing_stop_positive = 0.002  # 追踪止损触发点：0.2%盈利
    trailing_stop_positive_offset = 0.03  # 追踪止损偏移：3%
    trailing_only_offset_is_reached = True  # 只有达到偏移量才启用追踪止损

    use_custom_stoploss = False  # 不使用自定义止损函数

    # ===== 超参数优化配置 =====
    is_optimize_32 = True  # 启用买入条件超参数优化

    # 买入条件1的超参数（可优化）
    buy_rsi_fast_32 = IntParameter(
        20, 70, default=40, space="buy", optimize=is_optimize_32
    )  # RSI快线阈值：20-70，默认40
    buy_rsi_32 = IntParameter(
        15, 50, default=42, space="buy", optimize=is_optimize_32
    )  # RSI阈值：15-50，默认42
    buy_sma15_32 = DecimalParameter(
        0.900, 1, default=0.973, decimals=3, space="buy", optimize=is_optimize_32
    )  # SMA15倍数：0.9-1.0，默认0.973
    buy_cti_32 = DecimalParameter(
        -1, 1, default=0.69, decimals=2, space="buy", optimize=is_optimize_32
    )  # CTI阈值：-1到1，默认0.69

    # 卖出条件超参数
    sell_fastx = IntParameter(
        50, 100, default=84, space="sell", optimize=True
    )  # STOCH FastK阈值：50-100，默认84

    @property
    def protections(self):
        """
        保护机制配置

        设置冷却期保护，防止频繁交易
        """
        return [{"method": "CooldownPeriod", "stop_duration_candles": 96}]  # 96个K线冷却期（8小时）

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的各种技术指标：
        1. RSI系列：快速、标准、慢速RSI用于多时间框架分析
        2. SMA：简单移动平均线用于价格位置判断
        3. CTI：相对趋势指数用于趋势强度过滤
        4. STOCH：随机指标用于超买超卖判断
        5. CCI：商品通道指数用于退出信号

        Args:
            dataframe: OHLCV价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了技术指标的数据框
        """
        # ===== 买入信号相关指标 =====
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)  # 15周期简单移动平均线
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)  # 20周期相对趋势指数
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)  # 14周期标准RSI
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)  # 4周期快速RSI
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)  # 20周期慢速RSI

        # ===== 卖出信号相关指标 =====
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)  # 快速随机指标
        dataframe["fastk"] = stoch_fast["fastk"]  # 随机指标K值

        dataframe["cci"] = ta.CCI(dataframe, timeperiod=20)  # 20周期商品通道指数

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号

        定义两种买入策略：
        1. buy_1：可优化参数的买入策略
        2. buy_new：固定参数的买入策略

        两种策略的共同逻辑：
        - RSI慢线下降（动量减弱）
        - RSI快线低位（短期超卖）
        - RSI标准线高于阈值（避免深度超卖）
        - 价格低于SMA（价格回调）
        - CTI低于阈值（趋势强度过滤）

        Args:
            dataframe: 包含技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # ===== 买入策略1：可优化参数 =====
        buy_1 = (
            (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))  # RSI慢线下降
            & (dataframe["rsi_fast"] < self.buy_rsi_fast_32.value)  # RSI快线低于阈值
            & (dataframe["rsi"] > self.buy_rsi_32.value)  # RSI标准线高于阈值
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma15_32.value)  # 价格低于SMA
            & (dataframe["cti"] < self.buy_cti_32.value)  # CTI低于阈值
        )

        # ===== 买入策略2：固定参数 =====
        buy_new = (
            (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))  # RSI慢线下降
            & (dataframe["rsi_fast"] < 34)  # RSI快线低于34
            & (dataframe["rsi"] > 28)  # RSI标准线高于28
            & (dataframe["close"] < dataframe["sma_15"] * 0.96)  # 价格低于SMA的96%
            & (dataframe["cti"] < self.buy_cti_32.value)  # CTI低于阈值（使用优化参数）
        )

        # ===== 添加买入条件和标签 =====
        conditions.append(buy_1)
        dataframe.loc[buy_1, "enter_tag"] += "buy_1"  # 标记为买入策略1

        conditions.append(buy_new)
        dataframe.loc[buy_new, "enter_tag"] += "buy_new"  # 标记为买入策略2

        # ===== 设置最终入场信号 =====
        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1
        return dataframe

    def custom_exit(
        self,
        pair: str,
        trade: "Trade",
        current_time: "datetime",
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        自定义退出逻辑

        实现两种退出策略：
        1. 盈利退出：当有盈利且STOCH FastK超过阈值时退出
        2. 止损退出：当亏损不超过3%且CCI超过80时退出

        这种设计允许在小幅盈利时及时获利了结，
        在轻微亏损时通过技术指标信号止损。

        Args:
            pair: 交易对
            trade: 当前交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈亏比例
            **kwargs: 其他参数

        Returns:
            str or None: 退出标签，None表示不退出
        """
        # 获取当前K线数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair=pair, timeframe=self.timeframe)
        current_candle = dataframe.iloc[-1].squeeze()

        # 计算最小盈利率（用于后续扩展）
        min_profit = trade.calc_profit_ratio(trade.min_rate)

        # ===== 盈利退出策略 =====
        if current_profit > 0:  # 当前有盈利
            if current_candle["fastk"] > self.sell_fastx.value:  # STOCH FastK超过阈值
                return "fastk_profit_sell"  # 盈利退出

        # ===== 止损退出策略 =====
        if current_profit > -0.03:  # 亏损不超过3%
            if current_candle["cci"] > 80:  # CCI超过80（超买）
                return "cci_loss_sell"  # 止损退出

        return None  # 不退出

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充退出信号

        由于策略完全依赖custom_exit函数进行退出决策，
        此函数不设置任何基于技术指标的退出信号。
        所有退出逻辑都在custom_exit中实现。

        Args:
            dataframe: 包含技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 不包含退出信号的数据框
        """
        # 不设置任何退出信号，完全依赖custom_exit
        dataframe.loc[:, ["exit_long", "exit_tag"]] = (0, "long_out")
        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从config.json读取杠杆配置

        从配置文件中读取杠杆设置，如果配置文件中没有设置则使用默认值1.0

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """
        # 从配置文件中读取杠杆设置
        config_leverage = self.config.get("leverage", 1.0)

        # 确保杠杆在合理范围内
        leverage = max(1.0, min(config_leverage, max_leverage))

        return leverage
