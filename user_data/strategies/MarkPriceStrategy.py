from freqtrade.strategy import IStrategy
from pandas import DataFrame
import pandas as pd

class MarkPriceStrategy(IStrategy):
    """
    使用标记价格数据的策略示例
    """
    
    timeframe = '5m'
    
    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        添加标记价格相关指标
        """
        pair = metadata['pair']
        
        # 获取8小时标记价格数据
        mark_df = self.dp.get_pair_dataframe(
            pair=pair,
            timeframe='8h',
            candle_type='mark'  # 指定获取标记价格数据
        )
        
        if not mark_df.empty:
            # 重命名列以避免冲突
            mark_df = mark_df.rename(columns={
                'open': 'mark_open',
                'high': 'mark_high', 
                'low': 'mark_low',
                'close': 'mark_close',
                'volume': 'mark_volume'
            })
            
            # 将8小时数据重采样到5分钟
            mark_resampled = mark_df.set_index('date').resample('5T').ffill()
            
            # 合并到主数据框
            df = df.merge(mark_resampled[['mark_close']], 
                         left_on='date', right_index=True, how='left')
            
            # 计算现货价格与标记价格的差异
            df['price_mark_diff'] = df['close'] - df['mark_close']
            df['price_mark_ratio'] = df['close'] / df['mark_close']
            df['price_mark_diff_pct'] = (df['price_mark_diff'] / df['mark_close']) * 100
            
            # 标记价格趋势指标
            df['mark_sma_20'] = df['mark_close'].rolling(20).mean()
            df['mark_trend'] = df['mark_close'] > df['mark_sma_20']
            
        return df
    
    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        基于标记价格差异的入场逻辑
        """
        # 当现货价格显著低于标记价格时做多（可能的套利机会）
        df.loc[
            (
                (df['price_mark_diff_pct'] < -0.1) &  # 现货价格低于标记价格0.1%
                (df['mark_trend'] == True) &          # 标记价格处于上升趋势
                (df['volume'] > df['volume'].rolling(20).mean())
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'mark_price_discount')
        
        # 当现货价格显著高于标记价格时做空
        df.loc[
            (
                (df['price_mark_diff_pct'] > 0.1) &   # 现货价格高于标记价格0.1%
                (df['mark_trend'] == False) &         # 标记价格处于下降趋势
                (df['volume'] > df['volume'].rolling(20).mean())
            ),
            ['enter_short', 'enter_tag']
        ] = (1, 'mark_price_premium')
        
        return df