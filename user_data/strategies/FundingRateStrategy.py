from freqtrade.strategy import IStrategy
import pandas as pd

class FundingRateStrategy(IStrategy):
    """
    基于资金费率的交易策略示例
    """
    
    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        添加资金费率指标
        """
        # 获取资金费率数据
        pair = metadata['pair']
        funding_df = self.dp.get_pair_dataframe(
            pair=pair, 
            timeframe='8h',
            candle_type='funding_rate'
        )
        
        if not funding_df.empty:
            # 将8小时资金费率数据重采样到策略时间框架
            funding_resampled = funding_df.resample('5T', on='date').ffill()
            
            # 合并到主数据框
            df = df.merge(funding_resampled[['funding_rate']], 
                         left_on='date', right_index=True, how='left')
            
            # 计算资金费率指标
            df['funding_rate_ma'] = df['funding_rate'].rolling(24).mean()  # 24期移动平均
            df['funding_rate_extreme'] = (df['funding_rate'].abs() > 0.01)  # 极端费率标记
        
        return df
    
    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        基于资金费率的入场逻辑
        """
        df.loc[
            (
                (df['funding_rate'] > 0.005) &  # 高正资金费率，市场过度看多
                (df['rsi'] > 70) &              # RSI超买
                (df['volume'] > df['volume'].rolling(20).mean())
            ),
            ['enter_short', 'enter_tag']
        ] = (1, 'high_funding_short')
        
        df.loc[
            (
                (df['funding_rate'] < -0.005) &  # 高负资金费率，市场过度看空
                (df['rsi'] < 30) &               # RSI超卖
                (df['volume'] > df['volume'].rolling(20).mean())
            ),
            ['enter_long', 'enter_tag']
        ] = (1, 'negative_funding_long')
        
        return df