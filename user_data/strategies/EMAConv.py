import warnings
from datetime import datetime, timedelta

import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame

# 过滤pandas_ta库的弃用警告
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")


class EMAConv(IStrategy):
    """
    EMA收敛交易策略（带基于时间的止损）

    基于快速EMA和EMA200之间的比例关系和宽度变化进行单向交易的策略。

    策略核心逻辑：
    1. 做多条件：快速EMA小于EMA200一定比例且两线宽度正在减小（收敛）
    2. 退出条件：快速EMA和EMA200发生交叉时退出所有仓位
    3. 基于时间的止损：根据持仓时间设置不同的止损阈值
       - 超过1小时：最大止损20%
       - 超过2小时：最大止损10%
       - 超过4小时：最大止损5%

    技术指标：
    - EMA：可优化周期的指数移动平均线（快线，默认20周期）
    - EMA200：200周期指数移动平均线（慢线）
    - 比例差异：(EMA - EMA200) / EMA200 * 100
    - 宽度变化：当前周期与前一周期的差异变化

    风险控制：
    - 基于时间的动态止损机制
    - 极端亏损保护（30%强制退出）
    - EMA交叉退出信号

    适用场景：
    - 趋势收敛阶段的反转交易
    - 中长期趋势跟踪
    - 需要严格时间风控的交易环境

    作者: FreqTrade用户
    时间框架: 建议5m-1h
    """

    timeframe = "5m"  # 推荐时间框架

    max_open_trades = 10  # 最大开仓数量

    startup_candle_count = 300  # 启动时需要的历史K线数量
    process_only_new_candles = True  # 仅在新K线时运行指标计算，提高效率
    position_adjustment_enable = True  # 启用仓位调整

    use_custom_stoploss = True  # 使用自定义止损函数

    # 订单类型配置 - 全部使用市价单确保快速成交
    order_types = {
        "entry": "market",  # 入场订单：市价单
        "exit": "market",  # 出场订单：市价单
        "emergency_exit": "market",  # 紧急出场：市价单
        "force_entry": "market",  # 强制入场：市价单
        "force_exit": "market",  # 强制出场：市价单
        "stoploss": "market",  # 止损订单：市价单
        "stoploss_on_exchange": False,  # 不在交易所设置止损
        "stoploss_on_exchange_interval": 60,  # 交易所止损检查间隔
        "stoploss_on_exchange_market_ratio": 0.99,  # 交易所止损市价比例
    }

    stoploss = -0.99  # 止损设置：设置为很大的负值，基本禁用止损，只依赖EMA交叉退出

    # ROI收益率表：相对保守的设置
    minimal_roi = {}

    # 买入超参数配置
    buy_params = {
        "buy_leverage": 3.0,
        "buy_long_ema_threshold": 2.46917,  # EMA做多比例阈值
        "buy_long_bblower_threshold": 4.67538,  # 布林带下轨做多比例阈值
        "buy_long_bblower_threshold2": 8.0,  # 布林带下轨做多比例阈值
        # 24小时价格变化范围
        "buy_market_24h_change_min": -20,
        "buy_market_24h_change_max": 9.2,
    }

    # 卖出超参数配置
    sell_params = {
        # 当EMA比例缩小到一定程度时止盈
        "sell_ema_ratio_take_threshold": 0.023,  # 止盈阈值（EMA比例缩小）
        "sell_take_ema_ratio_value": 3,  # 止盈EMA比例缩小
        # 价格突破布林带上轨止盈
        "sell_take_bb_upperband_threshold": 0.147,  # 布林带上轨突破止盈阈值
        # 强制止损
        "sell_loss_holding_minutes": 901,  # 强制退出持仓时间（分钟）
        "sell_loss_profit_threshold": -0.035,  # 强制退出亏损阈值
    }

    buy_leverage = DecimalParameter(1.0, 20.0, default=2.0, space="buy", decimals=1, optimize=True)

    # 超参数定义
    buy_long_ema_threshold = DecimalParameter(  # 做多比例阈值：EMA20低于EMA200的比例
        1.0, 6.0, default=4.424, space="buy", decimals=5, optimize=True
    )

    buy_long_bblower_threshold = DecimalParameter(1.0, 7.0, default=4.67538, space="buy", decimals=5, optimize=True)
    buy_long_bblower_threshold2 = DecimalParameter(6.0, 15.0, default=8.0, space="buy", decimals=5, optimize=True)

    buy_market_24h_change_min = DecimalParameter(-25.0, -5.0, default=-18.0, decimals=1, space="buy", optimize=True)
    buy_market_24h_change_max = DecimalParameter(5.0, 30.0, default=20.0, decimals=1, space="buy", optimize=True)

    # 卖出超参数定义
    # 强制退出亏损阈值：当亏损达到此百分比时考虑强制退出
    sell_loss_profit_threshold = DecimalParameter(-0.30, -0.02, default=-0.20, space="sell", decimals=3, optimize=True)

    # 强制退出持仓时间：持仓超过此分钟数时考虑强制退出
    sell_loss_holding_minutes = IntParameter(300, 1440, default=600, space="sell", optimize=True)

    # 止盈阈值：当盈利达到此百分比,并且价格突破布林带上轨时止盈
    sell_take_bb_upperband_threshold = DecimalParameter(0.02, 0.60, default=0.20, space="sell", decimals=3, optimize=True)

    # 止盈阈值：当盈利达到此百分比,并且EMA比例缩小到一定程度时止盈
    sell_ema_ratio_take_threshold = DecimalParameter(0.02, 0.60, default=0.20, space="sell", decimals=3, optimize=True)
    # # 止盈EMA宽度缩小
    sell_take_ema_ratio_value = DecimalParameter(1.0, 5.0, default=2, space="sell", decimals=3, optimize=True)

    # @informative("1h")
    # def populate_indicators_1h(self, df: DataFrame, metadata: dict) -> DataFrame:
    #     return df

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充技术指标

        计算策略所需的EMA指标和衍生指标：
        1. EMA：可优化周期的指数移动平均线（快线）
        2. EMA200：200周期指数移动平均线（慢线）
        3. 比例差异：两线之间的百分比差异
        4. 宽度：两线之间的绝对差值
        5. 宽度变化：用于判断收敛趋势

        Args:
            df: OHLCV价格数据
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了技术指标的数据框
        """

        # 收盘-开盘
        df["close_open"] = df["close"] - df["open"]

        # 计算布林带指标
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]  # 布林带下轨
        # df["bb_middleband"] = bollinger["mid"]  # 布林带中轨
        df["bb_upperband"] = bollinger["upper"]  # 布林带上轨

        # 计算EMA指标
        df["ema"] = ta.EMA(df["close"], timeperiod=20)  # 快速EMA线
        df["ema_120"] = ta.EMA(df["close"], timeperiod=120)  # 100周期EMA慢线
        df["ema_200"] = ta.EMA(df["close"], timeperiod=200)  # 200周期EMA慢线
        df["ema_250"] = ta.EMA(df["close"], timeperiod=250)  # 200周期EMA慢线

        df["ema_250_ratio"] = (df["ema_200"] - df["ema_250"]) / df["ema_200"] * 100
        df["ema_250_ratio_change"] = df["ema_250_ratio"] - df["ema_250_ratio"].shift(1)

        # 计算比例差异（小数形式）
        # 正值表示EMA低于EMA200，负值表示EMA高于EMA200
        df["ema_ratio"] = (df["ema_200"] - df["ema"]) / df["ema_200"] * 100
        df["ema_ratio_change"] = df["ema_ratio"] - df["ema_ratio"].shift(1)

        df["bblower_ratio"] = (df["ema_200"] - df["bb_lowerband"]) / df["ema_200"] * 100
        df["bblower_ratio_change"] = df["bblower_ratio"] - df["bblower_ratio"].shift(1)

        df["bblower_out_ratio"] = (df["bb_lowerband"] - df["close"]) / df["bb_lowerband"] * 100

        # === 随机指标 ===
        stoch_fast = ta.STOCHF(df, 5, 3, 0, 3, 0)
        df["fastk"] = stoch_fast["fastk"]
        df["fastd"] = stoch_fast["fastd"]

        # === 波动性指标 ===
        df["atr"] = ta.ATR(df, timeperiod=14)
        df["atr_pct"] = (df["atr"] / df["close"]) * 100

        # === 成交量指标 ===
        df["volume_sma"] = ta.SMA(df["volume"], timeperiod=20)
        df["volume_ratio"] = df["volume"] / df["volume_sma"]

        # === 市场环境指标 ===
        # 24小时价格变化百分比
        df["24h_change_pct"] = ((df["close"] - df["close"].shift(288)) / df["close"].shift(288)) * 100
        df["zero"] = 0
        return df

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场信号

        根据EMA比例关系和收敛条件生成买入信号：

        做多条件：
        1. 快速EMA低于EMA200超过设定阈值（buy_ema_threshold，小数形式）
        2. 两线宽度连续收敛（减小）达到设定周期数
        3. 当前无金叉发生（避免在交叉时入场）

        Args:
            df: 包含技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """

        # 基本条件，买入必须满足的条件
        base_condition = (
            True  # 调试时保留
            & (df["volume"] > 0)  # 有交易量
        )

        # 做多必须满足的条件
        long_condition = (
            base_condition  # 基本条件
            & (df["open"] > df["close"])  # 收盘价高于开盘价，避免连续下跌时买入
            & (df["open"].shift(1) < df["bb_lowerband"])  # 开盘价低于布林带下轨
            # & (df["24h_change_pct"].notna())  # 开盘价低于布林带下轨
            # & (df["24h_change_pct"] > -12)
            # & (df["24h_change_pct"] < self.buy_market_24h_change_max.value)
        )

        # 做多信号：快速EMA显著低于EMA200且正在收敛
        long_ema_condition = (
            long_condition  # 基本条件
            & (df["ema_ratio"] > self.buy_long_ema_threshold.value)
            & (df["ema_ratio_change"] < -0.01)
        )
        long_bblower_condition = (
            long_condition
            & (df["bblower_ratio"] > self.buy_long_bblower_threshold.value)
            & (df["bblower_ratio_change"] < -0.02)
            & (df["ema_ratio_change"] < -0.02)
        )

        long_bblower_condition2 = (
            long_condition
            & (df["bblower_ratio"] > self.buy_long_bblower_threshold2.value)  # 比例高于阈值（EMA低于EMA200）
            & (df["bblower_ratio_change"] < -0.01)
        )

        long_fskt_condition = (
            long_condition  #
            & (df["fastk"] < 20)
            & (df["fastd"] < 20)
            & (df["ema_ratio_change"] < -0.01)
        )

        df.loc[long_ema_condition, ["enter_long", "enter_tag"]] = (1, "ema_convergence")
        df.loc[long_bblower_condition, ["enter_long", "enter_tag"]] = (1, "bblower_convergence")
        df.loc[long_bblower_condition2, ["enter_long", "enter_tag"]] = (1, "bblower_convergence2")
        df.loc[long_fskt_condition, ["enter_long", "enter_tag"]] = (1, "fsk_convergence")

        return df

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time: datetime,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> bool:
        return True

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        return df

    def custom_exit(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ):
        """
        自定义退出逻辑

        多种退出条件组合：
        1. 极端亏损保护：亏损超过50%时强制退出
        2. 基于时间的止损：根据持仓时间设置不同的止损阈值
           - 超过1小时：最大止损20%
           - 超过2小时：最大止损10%
           - 超过4小时：最大止损5%
        3. 时间+亏损退出：持仓时间过长且亏损达到阈值时退出
        4. 止盈退出：盈利达到阈值且价格突破布林带上轨时退出
        5. EMA交叉退出：EMA比例回归时退出

        Args:
            pair: 交易对
            trade: 当前交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前收益率

        Returns:
            str or None: 退出原因标签，None表示不退出
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)  # 获取分析后的数据
        df = dataframe.iloc[-1].squeeze()  # 获取最后一行数据
        # 计算持仓时间
        holding_time = current_time - trade.open_date_utc
        holding_hours = holding_time.total_seconds() / 3600  # 转换为小时

        # 极端亏损保护：如果亏损超过30%，强制退出
        if current_profit <= -0.30:
            return "extreme_loss"

        # 基于时间的止损逻辑
        if holding_hours >= 4.0:  # 超过4小时
            if current_profit <= -0.05:  # 止损5%
                return "time_stoploss_4h"
        elif holding_hours >= 2.0:  # 超过2小时
            if current_profit <= -0.10:  # 止损10%
                return "time_stoploss_2h"
        elif holding_hours >= 1.0:  # 超过1小时
            if current_profit <= -0.20:  # 止损20%
                return "time_stoploss_1h"

        # 如果亏损超过设定阈值，同时持仓时间超过设定分钟数，强制退出
        if current_profit <= self.sell_loss_profit_threshold.value:
            if holding_time > timedelta(minutes=self.sell_loss_holding_minutes.value):
                return "loss_timeout"

        # 如果盈利超过设定阈值，同时价格高于布林带上轨，止盈
        if current_profit > self.sell_take_bb_upperband_threshold.value:
            if df["close"] > df["bb_upperband"]:
                return "take_bbupper_out"

        # 如果盈利超过设定阈值，同时EMA比例缩小到一定程度，止盈
        if current_profit > self.sell_ema_ratio_take_threshold.value:
            if df["ema_ratio"] < self.sell_take_ema_ratio_value.value:
                return "take_ema_reduce"

        if df["ema_ratio"] < 0.003:
            return "ema_cross"

        return None

    def leverage(
        self,
        pair: str,  # 交易对
        current_time: datetime,  # 当前时间
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆设置函数

        优先从config.json读取杠杆设置，如果没有配置则使用默认值

        Returns:
            float: 杠杆倍数
        """
        # 从配置文件读取杠杆，如果没有则使用默认值1.0
        config_leverage = self.config.get("leverage", 1.0)

        # 从超参数读取杠杆
        hyperopt_leverage = self.buy_leverage.value

        # 确保杠杆在合理范围内
        return min(max(config_leverage, hyperopt_leverage), max_leverage)
