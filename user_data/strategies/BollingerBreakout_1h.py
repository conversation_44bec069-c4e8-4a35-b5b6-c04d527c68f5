import warnings
from datetime import datetime, timedelta

import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IStrategy, informative
from pandas import DataFrame

# 过滤pandas_ta库的弃用警告
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")


class BollingerBreakout_1h(IStrategy):
    """
    双时间框架布林带突破策略

    基于5分钟和1小时布林带的双重突破信号进行双向交易的策略。

    策略核心逻辑：
    1. 做多条件：价格同时突破5分钟和1小时布林带下轨（超卖信号）
    2. 做空条件：价格同时突破5分钟和1小时布林带上轨（超买信号）
    3. 退出条件：价格回归到5分钟布林带中轨附近

    技术指标：
    - 5分钟布林带：基于5分钟K线计算的布林带（主要信号）
    - 1小时布林带：基于1小时K线计算的布林带（确认信号）
    - 布林带参数：20周期，2倍标准差（可优化）

    双重确认机制：
    - 只有当价格同时突破两个时间框架的布林带时才入场
    - 这样可以减少假突破，提高信号质量

    风险控制：
    - 支持双向交易（做多和做空）
    - 基于布林带中轨的退出机制
    - 可配置的止损和盈利目标

    适用场景：
    - 震荡市场中的突破交易
    - 多时间框架确认的高质量信号
    - 需要减少假突破的交易环境

    作者: FreqTrade用户
    时间框架: 5分钟（主）+ 1小时（辅助）
    """

    # 基础设置
    max_open_trades = 10
    timeframe = "5m"  # 主时间框架
    can_short = True  # 启用做空交易

    # 基础风险参数
    stoploss = -0.15  # 15%止损
    minimal_roi = {}

    # 退出参数
    exit_bb_middle_threshold = DecimalParameter(0.995, 1.005, default=1.0, space="sell", optimize=True)

    # 杠杆设置
    leverage_value = DecimalParameter(1.0, 5.0, default=3.0, space="buy", optimize=True)

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        设置杠杆倍数

        优先从config.json读取杠杆设置，如无配置则使用策略内部超参数

        Returns:
            float: 杠杆倍数（1.0到max_leverage之间）
        """
        # 尝试从config.json读取杠杆设置
        if hasattr(self.config, "get") and "trading" in self.config:
            config_leverage = self.config.get("trading", {}).get("leverage", None)
            if config_leverage is not None:
                return min(max(float(config_leverage), 1.0), max_leverage)

        # 如果config.json中没有设置，使用策略内部超参数
        return min(max(float(self.leverage_value.value), 1.0), max_leverage)

    @informative("1h")
    def populate_indicators_1h(self, df: DataFrame, metadata: dict) -> DataFrame:
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]
        df["bb_middleband"] = bollinger["mid"]
        df["bb_upperband"] = bollinger["upper"]

        return df

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]
        df["bb_middleband"] = bollinger["mid"]
        df["bb_upperband"] = bollinger["upper"]

        return df

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # 做多条件：价格同时突破5分钟和1小时布林带下轨
        long_condition = (
            (dataframe["volume"] > 0)  # 确保有交易量
            & (dataframe["close"] < dataframe["bb_lowerband"])  # 5分钟下轨突破
            & (dataframe["close"] < dataframe["bb_lowerband_1h"])  # 1小时下轨突破
        )

        # 做空条件：价格同时突破5分钟和1小时布林带上轨
        short_condition = (
            (dataframe["volume"] > 0)  # 确保有交易量
            & (dataframe["close"] > dataframe["bb_upperband"])  # 5分钟上轨突破
            & (dataframe["close"] > dataframe["bb_upperband_1h"])  # 1小时上轨突破
        )

        # 设置入场信号
        dataframe.loc[long_condition, ["enter_long", "enter_tag"]] = (1, "dual_bb_long")
        dataframe.loc[short_condition, ["enter_short", "enter_tag"]] = (1, "dual_bb_short")

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        生成退出信号

        基于价格回归布林带中轨生成退出信号：
        1. 多头退出：价格回到5分钟布林带中轨之上
        2. 空头退出：价格回到5分钟布林带中轨之下

        Args:
            dataframe: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了退出信号的数据框
        """
        # 多头退出条件：价格回到5分钟布林带中轨之上
        exit_long_condition = dataframe["close"] > dataframe["bb_middleband"] * self.exit_bb_middle_threshold.value

        # 空头退出条件：价格回到5分钟布林带中轨之下
        exit_short_condition = dataframe["close"] < dataframe["bb_middleband"] * self.exit_bb_middle_threshold.value

        # 设置退出信号
        dataframe.loc[exit_long_condition, ["exit_long", "exit_tag"]] = (1, "bb_middle_return")
        dataframe.loc[exit_short_condition, ["exit_short", "exit_tag"]] = (1, "bb_middle_return")

        return dataframe

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> str | None:
        """
        自定义退出逻辑

        提供额外的退出条件，补充基础的退出信号

        Args:
            pair: 交易对
            trade: 当前交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利率

        Returns:
            str | None: 退出标签或None（不退出）
        """
        # 获取最新的技术指标数据
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        if len(dataframe) < 1:
            return None

        last_candle = dataframe.iloc[-1].squeeze()

        # 极端盈利保护：盈利超过20%时部分退出
        if current_profit > 0.20:
            return "extreme_profit_exit"

        # 极端亏损保护：亏损超过12%时强制退出
        if current_profit < -0.12:
            return "extreme_loss_exit"

        # 时间止损：持仓超过4小时且亏损时退出
        if current_time - trade.open_date_utc > timedelta(hours=4) and current_profit < 0:
            return "time_stop_exit"

        return None
