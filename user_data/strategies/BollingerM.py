from datetime import datetime

import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame


class Bollinger_1m(IStrategy):
    """
    布林带变化率交易策略

    基于布林带轨道变化率进行双向交易的策略。

    策略核心逻辑：
    1. 做多条件：布林带上轨和中轨同时向上变化，变化率超过阈值
    2. 做空条件：布林带下轨和中轨同时向下变化，变化率超过阈值
    3. 退出条件：价格回归到布林带中轨附近

    技术指标：
    - 布林带：20周期，2倍标准差的布林带
    - 变化率计算：(当前值 - 前一周期值) / 当前值
    - 上轨变化率：bbu_change
    - 中轨变化率：bbm_change
    - 下轨变化率：bbl_change

    入场逻辑：
    1. 布林带SMA策略：
       - 做多：当布林带上轨和中轨都向上变化且变化率大于阈值时入场
       - 做空：当布林带下轨和中轨都向下变化且变化率小于阈值时入场
    2. 价格突破策略（独立条件）：
       - 做多：当收盘价高于N小时最高价一定比例时入场
       - 做空：当收盘价低于N小时最低价一定比例时入场
    3. 通用过滤条件：
       - 价格变化范围过滤：只在N小时内价格变化范围不大的时段开单，避免高波动期间交易
       - 所有阈值通过超参数优化确定最佳值

    风险控制：
    - 支持双向交易（做多和做空）
    - 15%固定止损保护
    - 基于布林带中轨的退出机制
    - 反转信号亏损退出：当出现反转信号且亏损达到阈值时退出
    - 极端盈利/亏损保护
    - 时间止损机制

    适用场景：
    - 波动率突然增加的市场
    - 布林带快速扩张或收缩时的交易机会
    - 短期动量交易

    作者: FreqTrade用户
    时间框架: 1分钟
    """

    # 基础设置
    timeframe = "1m"  # 主时间框架
    can_short = True  # 启用做空交易

    # Buy hyperspace params:
    buy_params = {
        "buy_leverage": 20.0,
        #
        "buy_bb_sma_window": 3,
        "buy_bbl_sma_threshold": -0.007,
        "buy_bbu_sma_threshold": 0.001,
        #
        "buy_price_range_num": 20,
        "buy_price_range_threshold": 0.005,
        #
        "buy_breakout_long_threshold": 0.005,
        "buy_breakout_short_threshold": 0.005,
    }

    # Sell hyperspace params:
    sell_params = {
        "sell_bbm_loss_profit_threshold": -0.183,
        "sell_bbm_take_profit_threshold": 0.275,
        #
        "sell_reversal_loss_threshold": -0.084,
        "sell_reversal_profit_threshold": 0.27,
        #
        "sell_long_fastx": 90,
        "sell_short_fastx": 90,
    }

    # Stoploss:
    stoploss = -0.3

    # Trailing stop:
    trailing_stop = False

    # Max Open Trades:
    max_open_trades = 5  # value loaded from strategy

    # =============================================================================
    # 杠杆设置
    # =============================================================================
    is_hyperopt_leverage = True
    buy_leverage = DecimalParameter(1.0, 15.0, default=10.0, space="buy", optimize=is_hyperopt_leverage)  # 杠杆倍数（1-15倍）

    # =============================================================================
    # 价格变化范围过滤参数
    # =============================================================================
    buy_price_range_num = IntParameter(10, 100, default=15, space="buy", optimize=True)  # N根K线时间窗口
    buy_price_range_threshold = DecimalParameter(0.0001, 0.1, default=0.005, space="buy", optimize=True)  # 价格变化范围阈值（5%默认）

    # =============================================================================
    # 布林带SMA策略参数
    # =============================================================================
    is_bb_sma_enable = True
    buy_bb_sma_window = IntParameter(3, 10, default=3, space="buy", optimize=is_bb_sma_enable)  # 布林带SMA窗口
    buy_bbl_sma_threshold = DecimalParameter(-0.02, -0.001, default=-0.007, space="buy", optimize=is_bb_sma_enable)  # 下轨SMA阈值
    buy_bbu_sma_threshold = DecimalParameter(0.001, 0.02, default=0.001, space="buy", optimize=is_bb_sma_enable)  # 上轨SMA阈值

    # =============================================================================
    # 布林带中线穿越参数
    # =============================================================================
    is_bbm_cross_enable = False
    buy_bbw_sma_window = IntParameter(3, 8, default=4, space="buy", optimize=is_bbm_cross_enable)
    buy_bbw_expand_threshold = DecimalParameter(0.0001, 0.01, default=0.003, space="buy", optimize=is_bbm_cross_enable)

    # =============================================================================
    # 退出参数
    # =============================================================================
    is_fastk_enable = False
    sell_long_fastx = IntParameter(50, 100, default=75, space="sell", optimize=is_fastk_enable)
    sell_short_fastx = IntParameter(50, 100, default=75, space="sell", optimize=is_fastk_enable)

    # =============================================================================
    # 布林带中轨亏损保护阈值
    # =============================================================================
    is_bbm_exit_enable = True
    # 布林带中轨亏损保护阈值
    sell_bbm_loss_profit_threshold = DecimalParameter(-0.2, -0.0001, default=-0.059, space="sell", optimize=is_bbm_exit_enable)
    # 布林带中轨盈利保护阈值
    sell_bbm_take_profit_threshold = DecimalParameter(0.1, 0.5, default=0.308, space="sell", optimize=is_bbm_exit_enable)

    # =============================================================================
    # 反转信号退出参数
    # =============================================================================
    is_reversal_exit_enable = True
    # 反转信号亏损退出阈值
    sell_reversal_loss_threshold = DecimalParameter(-0.15, -0.001, default=-0.02, space="sell", optimize=is_reversal_exit_enable)
    # 反转信号止盈阈值
    sell_reversal_profit_threshold = DecimalParameter(0.01, 0.3, default=0.05, space="sell", optimize=is_reversal_exit_enable)

    # =============================================================================
    # 保护机制参数
    # =============================================================================
    is_protection_cooldown = False
    cooldown_lookback = IntParameter(2, 10, default=5, space="protection", optimize=is_protection_cooldown)  # 冷却期周期数（2-10根K线）

    @property
    def protections(self):
        """
        配置保护机制

        CooldownPeriod保护：
        - 防止交易对在退出交易后立即重新入场
        - 给其他交易对提供交易机会
        - 避免在同一交易对上过度频繁交易

        Returns:
            list: 保护机制配置列表
        """
        results = []
        if self.is_protection_cooldown:
            results.append({"method": "CooldownPeriod", "stop_duration_candles": self.cooldown_lookback.value})
        return results

    def populate_indicators(self, df: DataFrame, metadata: dict) -> DataFrame:
        df["open_close"] = (df["close"] - df["open"]) / df["open"]

        # 计算布林带（20周期，2倍标准差）
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(df), window=20, stds=2)
        df["bb_lowerband"] = bollinger["lower"]
        df["bb_middleband"] = bollinger["mid"]
        df["bb_upperband"] = bollinger["upper"]
        df["bb_width"] = (df["bb_upperband"] - df["bb_lowerband"]) / df["bb_upperband"]
        df["bbw_sma"] = df["bb_width"].rolling(window=self.buy_bbw_sma_window.value, min_periods=1).mean()

        # 计算价格相对于布林带中轨的位置
        df["bbm_pos_open"] = (df["open"] - df["bb_middleband"]) / df["bb_middleband"]  # 开盘价相对中轨位置
        df["bbm_pos_close"] = (df["close"] - df["bb_middleband"]) / df["bb_middleband"]  # 收盘价相对中轨位置
        df["bbm_pos"] = df["bbm_pos_close"]  # 主要使用收盘价位置（正值=中轨上方，负值=中轨下方）

        # 计算布林带各轨道的变化率（当前值相对于前一周期的变化百分比）
        df["bbl_change"] = (df["bb_lowerband"] - df["bb_lowerband"].shift(1)) / df["bb_lowerband"]  # 下轨变化率
        df["bbm_change"] = (df["bb_middleband"] - df["bb_middleband"].shift(1)) / df["bb_middleband"]  # 中轨变化率
        df["bbu_change"] = (df["bb_upperband"] - df["bb_upperband"].shift(1)) / df["bb_upperband"]  # 上轨变化率
        # 布林带轨道突破
        df["bbl_breakout"] = (df["close"] < df["bb_lowerband"]) & (df["bbu_change"] > 0)
        df["bbu_breakout"] = (df["close"] > df["bb_upperband"]) & (df["bbl_change"] < 0)

        df["bbl_error"] = (df["bbl_change"] < 0) & (df["bbm_change"] < 0) & (df["open_close"] > 0)
        df["bbu_error"] = (df["bbu_change"] > 0) & (df["bbm_change"] > 0) & (df["open_close"] < 0)

        # 布林带轨道均线
        df["bbl_sma"] = df["bb_lowerband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()
        df["bbm_sma"] = df["bb_middleband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()
        df["bbu_sma"] = df["bb_upperband"].rolling(window=self.buy_bb_sma_window.value, min_periods=1).mean()
        df["bbw_sma"] = df["bb_width"].rolling(window=self.buy_bbw_sma_window.value, min_periods=1).mean()

        df["bbl_sma_ratio"] = (df["bb_lowerband"] - df["bbl_sma"]) / df["bb_lowerband"]
        df["bbm_sma_ratio"] = (df["bb_middleband"] - df["bbm_sma"]) / df["bb_middleband"]
        df["bbu_sma_ratio"] = (df["bb_upperband"] - df["bbu_sma"]) / df["bb_upperband"]

        # 计算布林带SMA策略条件
        df["bb_sma_long"] = (df["bbl_sma_ratio"] < self.buy_bbl_sma_threshold.value) & (df["bbm_sma_ratio"] < 0)  # 做多条件：下轨和中轨都向下
        df["bb_sma_short"] = (df["bbu_sma_ratio"] > self.buy_bbu_sma_threshold.value) & (df["bbm_sma_ratio"] > 0)  # 做空条件：上轨和中轨都向上

        # Cofi
        stoch_fast = ta.STOCHF(df, 5, 3, 0, 3, 0)
        df["fastd"] = stoch_fast["fastd"]
        df["fastk"] = stoch_fast["fastk"]
        df["adx"] = ta.ADX(df)

        # 计算价格变化范围过滤器
        price_range = df["close"].rolling(window=self.buy_price_range_num.value, min_periods=1).agg(["min", "max"])
        df["price_range_min"] = price_range["min"]
        df["price_range_max"] = price_range["max"]
        df["price_range_width"] = (price_range["max"] - price_range["min"]) / price_range["max"]

        # 辅助指标
        df["zero"] = 0  # 零线，用于图表显示
        return df

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成入场信号

        1. 布林带SMA策略：
           - 做多：当布林带下轨和中轨都向下变化且变化率超过阈值时入场
           - 做空：当布林带上轨和中轨都向上变化且变化率超过阈值时入场
        2. 价格突破策略：
           - 做多：当开盘到收盘为正且满足其他条件时入场

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """

        # 初始化入场信号列
        df["enter_long"] = 0
        df["enter_short"] = 0
        df["enter_tag"] = ""

        # 计算基础条件
        base_condition = (
            True  # 始终为真的占位符
            & (df["volume"] > 0)  # 确保有交易量，避免在无交易时段入场
            # & (df["price_range_width"].shift(1) < self.buy_price_range_threshold.value)  # 价格变化范围过滤
        )

        # =============================================================================
        # 布林带SMA策略条件
        # =============================================================================
        df.loc[  # 做多条件：布林带下轨和中轨向下变化
            (
                base_condition  # 基本条件
                & (df["bb_sma_long"])  # 布林带SMA做多信号
                & (df["open_close"] > 0)  # 当前K线为阳线
            ),
            ["enter_long", "enter_tag"],
        ] = (1, "bb_sma_long")

        df.loc[  # 做空条件：布林带上轨和中轨向上变化
            (
                base_condition  # 基本条件
                & (df["bb_sma_short"])  # 布林带SMA做空信号
                & (df["bbu_sma_ratio"] > 0.0001)  # 上轨SMA比率过滤
            ),
            ["enter_short", "enter_tag"],
        ] = (1, "bb_sma_short")

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        生成退出信号

        基于价格回归布林带中轨生成退出信号，当价格回到布林带中轨附近时退出仓位

        Args:
            df: 包含所有技术指标的数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了退出信号的数据框
        """

        # 初始化退出信号列
        df["exit_long"] = 0
        df["exit_short"] = 0
        df["exit_tag"] = ""

        if self.is_fastk_enable:
            df.loc[qtpylib.crossed_above(df["fastk"], self.sell_long_fastx.value), ["exit_long", "exit_tag"]] = (1, "fastk_cross_exit_long")
            df.loc[qtpylib.crossed_below(df["fastk"], 100 - self.sell_short_fastx.value), ["exit_short", "exit_tag"]] = (1, "fastk_cross_exit_short")

        return df

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float, current_profit: float, **kwargs) -> str | None:
        """
        自定义退出逻辑

        提供额外的风险控制退出条件，包括反转信号退出和布林带中轨退出

        退出条件：
        1. 反转信号退出：当出现与当前仓位相反的入场信号时退出
           - 做多仓位：出现bb_sma_short信号时，根据盈亏状态决定退出
             * 盈利超过阈值：反转止盈退出
             * 亏损超过阈值：反转止损退出
           - 做空仓位：出现bb_sma_long信号时，根据盈亏状态决定退出
             * 盈利超过阈值：反转止盈退出
             * 亏损超过阈值：反转止损退出
        2. 布林带中轨退出：价格回归到布林带中轨时的盈亏保护
           - 做多：价格低于中轨时检查盈亏状态
           - 做空：价格高于中轨时检查盈亏状态

        Args:
            pair: 交易对名称
            trade: 当前交易对象，包含交易信息
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利率（正数为盈利，负数为亏损）
            **kwargs: 其他参数

        Returns:
            str | None: 退出标签或None（不退出）
        """

        # =============================================================================
        # 计算持仓时间
        # =============================================================================
        # holding_time = current_time - trade.open_date_utc
        # holding_hours = holding_time.total_seconds() / 3600  # 转换为小时
        # # 时间止损：持仓超过4小时且处于亏损状态时退出
        # # 避免长时间持有亏损仓位，提高资金使用效率
        # if current_profit < 0:
        #     if holding_hours >= 4.0:
        #         return "time_loss_profit"

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)  # 获取分析后的数据
        if len(dataframe) < 1:
            return None
        df = dataframe.iloc[-1].squeeze()  # 获取最后一行数据

        # 做多仓位退出逻辑
        if not trade.is_short:
            # 检查反转信号：出现bb_sma_short信号时的退出逻辑
            if df.get("bb_sma_short", False):
                if current_profit > self.sell_reversal_profit_threshold.value:
                    return "reversal_profit_exit_long"  # 反转止盈退出
                elif current_profit < self.sell_reversal_loss_threshold.value:
                    return "reversal_loss_exit_long"  # 反转止损退出

            # 原有的布林带中轨退出逻辑
            if current_rate < df["bb_middleband"]:
                if current_profit < self.sell_bbm_loss_profit_threshold.value:
                    return "bbm_loss_exit_long"
                if current_profit > self.sell_bbm_take_profit_threshold.value:
                    return "bbm_profit_exit_long"
                if df["bbu_change"] < 0 and current_profit > 0:
                    return "bbu_reversal_exit_long"

        # 做空仓位退出逻辑
        else:
            # 检查反转信号：出现bb_sma_long信号时的退出逻辑
            if df.get("bb_sma_long", False):
                if current_profit > self.sell_reversal_profit_threshold.value:
                    return "reversal_profit_exit_short"  # 反转止盈退出
                elif current_profit < self.sell_reversal_loss_threshold.value:
                    return "reversal_loss_exit_short"  # 反转止损退出

            # 原有的布林带中轨退出逻辑
            if current_rate > df["bb_middleband"]:
                if current_profit < self.sell_bbm_loss_profit_threshold.value:
                    return "bbm_loss_exit_short"
                if current_profit > self.sell_bbm_take_profit_threshold.value:
                    return "bbm_profit_exit_short"
                if df["bbl_change"] > 0 and current_profit > 0:
                    return "bbl_reversal_exit_short"
        return None

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        设置杠杆倍数

        动态设置交易杠杆，优先从config.json读取全局设置，
        如无配置则使用策略内部的超参数优化值

        Args:
            pair: 交易对名称
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: FreqTrade建议的杠杆倍数
            max_leverage: 交易所允许的最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向（long/short）
            **kwargs: 其他参数

        Returns:
            float: 实际使用的杠杆倍数（1.0到max_leverage之间）
        """

        # 优先从config.json读取全局杠杆设置
        if hasattr(self.config, "get") and "trading" in self.config:
            config_leverage = self.config.get("trading", {}).get("leverage", None)
            if config_leverage is not None:
                return min(max(float(config_leverage), 1.0), max_leverage)

        # 如果config.json中没有设置，使用策略内部超参数
        return min(max(float(self.buy_leverage.value), 1.0), max_leverage)
