{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 5, "stake_currency": "USDT", "stake_amount": "unlimited", "datadir": "../data/okx", "data_format_ohlcv": "json", "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "dry_run_wallet": 1500, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "password": "", "ccxt_config": {"enableRateLimit": false, "httpsProxy": "http://localhost:3213", "wsProxy": "http://localhost:3213"}, "ccxt_async_config": {"enableRateLimit": false}, "pair_whitelist": ["BTC/USDT", "ETH/USDT", "LINK/USDT", "SOL/USDT", "ADA/USDT", "ALGO/USDT", "BNB/USDT", "LUNA/USDT", "LTC/USDT", "DOT/USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList", "number_assets": 20, "sort_key": "quoteVolume", "min_value": 0, "refresh_period": 1800}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "", "ws_token": "", "CORS_origins": [], "username": "freqtrader", "password": "1234"}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}