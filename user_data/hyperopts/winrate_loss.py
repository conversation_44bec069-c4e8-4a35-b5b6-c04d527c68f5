from freqtrade.optimize.hyperopt import IHyperOptLoss
from pandas import DataFrame


class WinRateLoss(IHyperOptLoss):
    """
    一个专门用于最大化胜率的损失函数。
    Hyperopt 的目标是最小化损失，所以我们返回 -winrate。
    这样，最小化 -winrate 就等于最大化 winrate。
    """

    @staticmethod
    def hyperopt_loss_function(results: DataFrame, **kwargs) -> float:
        # 从回测结果中获取总交易次数
        total_trades = len(results)
        if total_trades == 0:
            # 没有交易，返回一个较大的惩罚值
            return 1.0

        # 从回测结果中获取胜率
        # 'winrate' 是 Freqtrade 自动计算的指标之一
        winrate = results["profit_ratio"].gt(0).mean()  # 更精确的计算方式

        # 我们要最大化胜率，所以返回它的负值
        return -winrate
