---
type: "always_apply"
---

# FreqTrade 项目开发指南

## 1. 项目概述
这是一个基于FreqTrade的量化交易项目，专注于加密货币交易策略的开发、回测和优化。

## 2. 核心开发原则

### 2.1 语言与沟通
- 所有交流和文档使用中文
- 代码注释保持详细和清晰
- 遇到不确定问题立即询问，禁止猜测

### 2.2 开发环境
- 使用真实环境进行开发和测试
- 禁止使用模拟数据
- 所有配置通过配置文件管理，严禁硬编码
- **所有命令都会自动读取.env环境变量文件**
- **Python虚拟环境位置：/opt/freqtrade/.venv/**

## 3. 策略开发规范

### 3.1 策略偏好
- 优先开发高频交易策略（更多交易次数）
- 必须包含止损保护机制
- 支持多空双向交易
- 包含完整的超参数优化
- 重大优化前创建策略备份版本

### 3.2 参数命名规范
- 买入相关超参数使用'buy_'前缀
- 卖出相关超参数使用'sell_'前缀
- **所有入场条件参数必须使用space="buy"**：无论做多还是做空入场条件
- **退出条件参数使用space="sell"**：所有退出相关的参数
- 与freqtrade保持参数列表同步
- 命令行脚本包含短参数选项（如-d代替--data-dir）

## 4. 回测与优化

### 4.1 回测流程
- 使用`./backtesting.sh`命令运行回测

### 4.2 加密货币选择
- 排除BNB币种
- 仅选择在Binance交易所活跃的币种

## 5. 代码质量要求

### 5.1 编码规范
- 避免使用pandas Series.replace()的method参数（已弃用）
- 使用fillna()方法进行前向填充
- 保持详细的代码注释
- **DataFrame变量统一使用短名称df**：所有策略中的dataframe参数和变量统一使用df，包括函数参数、注释和文档字符串
- **条件表达式必须正确使用括号**：如 `(df["close"] < df["bb_lowerband"])` 而不是分行写法
- **必须实现populate_exit_trend方法**：即使使用custom_exit，FreqTrade仍要求此方法存在
- **双向交易策略必须添加做空声明**：`can_short = True`，FreqTrade默认禁用做空功能

### 5.2 测试要求
- **禁止创建测试脚本**：不要创建任何独立的测试脚本文件

## 6. 文件结构
```
.
├── user_data/
│   ├── strategies/          # 交易策略文件
│   ├── config.json         # 主配置文件
│   ├── backtest_results/   # 回测结果
│   └── hyperopt_results/   # 超参数优化结果
├── .augment/
│   └── rules/
│       └── rule.md         # 本指南文件
├── .env                    # 环境变量配置文件（所有命令自动读取）
└── *.sh                    # 各种执行脚本
```

## 7. 常用命令
- 回测：`./backtesting.sh`
- 超参数优化：`./hyperopt.sh`
- 下载数据：`./download.sh`
- 启动服务：`./start.sh`

## 8. 注意事项
- **强制要求**：每次任务完成后必须更新本指南
- **每次任务结束时必须同步修复和更新本指南文件**
- 修复Bug前必须先阅读本指南
- 所有API和代码必须通过context7验证
- 不确定的知识点必须进行网络搜索验证
- 检查.env文件是否包含所需的环境变量

### 8.1 任务结束规范
- **更新内容**：
  - 记录新的开发经验和解决方案
  - 更新策略开发记录
  - 添加新发现的问题及解决方法
  - 更新常用命令和最佳实践
  - 同步最新的配置和环境信息
- **更新时间**：任务完成的当天
- **版本控制**：在更新记录中详细说明修改内容

## 9. 策略开发经验总结

### 9.1 BollingerOut策略开发记录 (2025-07-23)
- **策略类型**：布林带突破策略
- **核心逻辑**：价格低于下轨做多，价格高于上轨做空
- **回测结果**：
  - 交易次数：222笔 (日均11.1笔) ✅ 高频交易
  - 胜率：59.9% ✅ 胜率良好
  - 总收益：-11.75% ❌ 整体亏损
  - 多头收益：+4.85% ✅ 多头策略有效
  - 空头收益：-16.61% ❌ 空头策略需优化
  - 最大回撤：15.38%

### 9.2 常见开发问题及解决方案
- **问题1**：`TypeError: unsupported operand type(s) for &: 'float' and 'bool'`
  - **原因**：条件表达式分行书写时未正确使用括号
  - **解决**：将条件用括号包围，如 `(df["close"] < df["bb_lowerband"])`
- **问题2**：`populate_exit_trend` must be implemented
  - **原因**：FreqTrade强制要求实现此方法，即使使用custom_exit
  - **解决**：添加空的populate_exit_trend方法实现
- **问题3**：`FutureWarning: Setting an item of incompatible dtype is deprecated`
  - **原因**：使用`df.fillna(0, inplace=True)`对包含datetime列的DataFrame进行操作
  - **解决**：只对特定的数值列使用fillna，如`df["maxup"].fillna(0)`和`df["maxdown"].fillna(0)`
- **问题4**：`UserWarning: pkg_resources is deprecated as an API`
  - **原因**：pandas_ta库使用了已弃用的pkg_resources API
  - **解决**：添加警告过滤器`warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")`
- **问题5**：`AttributeError: 'LocalTrade' object has no attribute 'is_long'`
  - **完整错误信息**：
    ```
    2025-07-24 21:15:29,839 - freqtrade.strategy.strategy_wrapper - ERROR - Unexpected error 'LocalTrade' object has no attribute 'is_long' calling <bound method EMAConvergenceStrategy.custom_exit of <EMAConvergenceStrategy.EMAConvergenceStrategy object at 0x113da2660>>
    Traceback (most recent call last):
      File "/opt/freqtrade/freqtrade/strategy/strategy_wrapper.py", line 31, in wrapper
        return f(*args, **kwargs)
      File "/Users/<USER>/freqAI/backtesting/user_data/strategies/EMAConvergenceStrategy.py", line 215, in custom_exit
        if trade.is_long and last_candle["death_cross"]:
           ^^^^^^^^^^^^^
    AttributeError: 'LocalTrade' object has no attribute 'is_long'
    ```
  - **错误代码**：`if trade.is_long and last_candle["death_cross"]:`
  - **原因**：FreqTrade的trade对象没有is_long属性，只有is_short属性
  - **解决**：使用`not trade.is_short`判断多头仓位，使用`trade.is_short`判断空头仓位
  - **正确代码**：`if not trade.is_short and last_candle["death_cross"]:`
  - **发生场景**：在custom_exit方法中进行多空仓位判断时
  - **影响范围**：所有使用trade.is_long的策略都会出现此错误
- **问题6**：策略没有做空交易
  - **现象**：策略代码包含做空逻辑，但回测结果显示0笔空头交易
  - **原因**：FreqTrade默认禁用做空功能，策略必须明确声明支持做空
  - **解决**：在策略类中添加 `can_short = True` 设置
  - **正确代码**：
    ```python
    class YourStrategy(IStrategy):
        # 启用做空交易
        can_short = True
    ```
  - **发生场景**：开发双向交易策略时
  - **重要性**：这是FreqTrade的安全机制，防止意外的做空交易
  - **验证方法**：回测结果中查看"Long / Short"统计信息
- **问题7**：`KeyError: '[nan] not in index'`
  - **完整错误信息**：
    ```
    File "/Users/<USER>/freqAI/backtesting/user_data/strategies/Bollinger_1mx5m.py", line 192, in populate_exit_trend
        df.loc[(df["enter_short"]), ["exit_long", "exit_tag"]] = (1, "short_signal_exit")
    KeyError: '[nan] not in index'
    ```
  - **错误代码**：`df.loc[(df["enter_short"]), ["exit_long", "exit_tag"]] = (1, "short_signal_exit")`
  - **原因**：在populate_entry_trend中创建的enter_long和enter_short列未初始化，包含NaN值，导致pandas索引错误
  - **解决**：在populate_entry_trend和populate_exit_trend方法开始时初始化所有信号列
  - **正确代码**：
    ```python
    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        # 初始化入场信号列
        df["enter_long"] = 0
        df["enter_short"] = 0
        df["enter_tag"] = ""

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        # 初始化退出信号列
        df["exit_long"] = 0
        df["exit_short"] = 0
        df["exit_tag"] = ""
        # 使用明确的布尔条件
        df.loc[(df["enter_short"] == 1), ["exit_long", "exit_tag"]] = (1, "short_signal_exit")
    ```
  - **发生场景**：策略使用df.loc直接设置信号列但未初始化时
  - **影响范围**：所有未正确初始化信号列的策略
  - **重要性**：FreqTrade要求所有信号列必须正确初始化，避免NaN值导致的索引错误

### 9.3 EMAConvergenceStrategy策略开发记录 (2025-07-24)
- **策略类型**：EMA收敛交易策略
- **核心逻辑**：基于EMA20和EMA200的比例关系和宽度收敛进行双向交易
- **技术特点**：
  - 做多条件：EMA20低于EMA200一定比例且宽度减小（收敛）
  - 做空条件：EMA20高于EMA200一定比例且宽度减小（收敛）
  - 退出条件：EMA20和EMA200发生交叉时退出
  - 支持超参数优化：比例阈值、收敛周期数
- **开发经验**：
  - 使用`["enter_long", "enter_tag"] = (1, "xxx")`语法设置入场信号
  - 修复`trade.is_long`属性错误，使用`not trade.is_short`判断多头
  - 实现完整的双向交易逻辑和自定义退出机制

### 9.4 BollingerCross角度计算功能开发记录 (2025-07-28)
- **功能类型**：布林带交叉角度过滤
- **核心功能**：
  - 计算布林带轨道的斜率角度
  - 计算两条布林带穿越时的角度
  - 通过角度阈值过滤弱信号
- **技术实现**：
  - `calculate_line_angle()`: 使用线性回归计算斜率角度
  - `calculate_cross_angle()`: 计算两条线差值的变化率角度
  - 使用`numpy.arctan()`和`numpy.degrees()`进行角度转换
- **超参数**：
  - `min_cross_angle`: 最小穿越角度阈值（0-45度，默认5度）
  - `angle_periods`: 计算角度的周期数（3-10周期，默认5周期）
- **应用效果**：
  - 过滤缓慢的假穿越信号
  - 只保留角度足够大的强烈穿越
  - 提高入场信号质量

### 9.5 策略优化建议
- **空头策略优化**：当前空头亏损严重，需要添加趋势过滤
- **退出逻辑改进**：考虑基于ATR的动态止损
- **参数优化**：通过hyperopt优化布林带参数和RSI过滤条件
- **角度优化**：通过回测优化最佳角度阈值和计算周期

## 10. 策略文档化规范

### 10.1 中文注释标准
- **函数文档字符串**：所有策略函数必须包含详细的中文文档字符串
- **参数说明**：每个超参数都要有中文注释说明其作用和取值范围
- **策略逻辑**：买入卖出条件必须有逐行中文注释
- **技术指标**：每个技术指标的计算和用途都要有中文说明
- **代码格式**：统一注释格式和代码风格，确保一致性
- **自定义函数**：所有自定义技术指标函数都要有完整的中文文档
- **示例格式**：
  ```python
  def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
      """
      填充技术指标

      计算策略所需的各种技术指标：
      1. RSI：相对强弱指标，用于识别超买超卖
      2. EMA：指数移动平均线，用于趋势判断

      Args:
          dataframe: OHLCV价格数据
          metadata: 交易对元数据

      Returns:
          DataFrame: 添加了技术指标的数据框
      """
      dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)  # 14周期RSI
  ```

### 10.2 已完成文档化的策略
- **BBMod.py** ✅ 已完成详细中文注释
  - 多条件布林带修改版策略
  - 包含12种不同的买入策略组合
  - 自定义动态止损机制
  - 所有技术指标和买入条件都有详细中文说明
- **BinHV27F.py** ✅ 已完成详细中文注释
  - 基于ADX和EMA-RSI的多条件交易策略
  - 4种不同的买入条件，3种卖出条件
  - 趋势强度和动量分析相结合
  - 所有指标计算和策略逻辑都有中文注释
- **ClucHAnix.py** ✅ 已完成详细中文注释
  - 基于Heiken Ashi和Fisher Transform的复合策略
  - 两种买入策略：精确入场和简单超卖
  - 自定义动态止损和杠杆设置
  - 完整的中文文档和逐行注释
- **E0V1EN.py** ✅ 已完成详细中文注释
  - 基于RSI和24小时价格变化的多条件交易策略
  - 两种买入策略：可优化参数和固定参数
  - 多层次自定义退出逻辑（盈利退出、指标止损、时间止损）
  - 冷却期保护机制和追踪止损
  - 完整的策略逻辑和参数说明
- **HourBasedStrategy.py** ✅ 已完成详细中文注释
  - 基于时间窗口的简单交易策略
  - 通过分钟级时间控制买入卖出时机
  - 支持超参数优化调整最佳交易时间窗口
  - 适合短期高频交易的时间驱动策略
  - 完整的策略文档和方法注释
- **ReinforcedQuickie.py** ✅ 已完成详细中文注释
  - 强化快速交易策略，专注上升趋势市场
  - 趋势强化：通过重采样确认长期趋势
  - 多指标确认：EMA、布林带、RSI、CCI、MFI等
  - 复杂的自定义止损和退出逻辑
  - 包含多种自定义技术指标：RMI、ZEMA、SSL通道等
  - 智能趋势跟踪和回撤控制机制
  - 8个自定义技术指标函数的完整文档
  - 所有策略方法的详细中文说明

## 11. 更新记录
- 创建时间：2025-07-22
- 最后更新：2025-08-04
- 更新内容：
  - 初始创建项目指南文件
  - 添加.env环境变量配置说明
  - 添加Python虚拟环境路径信息(/opt/freqtrade/.venv/)
  - 添加BollingerOut策略开发记录和经验总结
  - 更新技术指标使用规范
  - 修正回测命令和编码规范
  - **2025-07-23 新增**：添加任务结束规范，强制要求每次任务完成后必须同步更新本指南文件
  - **2025-07-23 修复**：解决BBMod策略中FutureWarning警告，修复rmi函数中的fillna操作
  - **2025-07-23 优化**：添加pandas_ta库UserWarning警告过滤，消除pkg_resources弃用警告
  - **2025-07-23 文档**：为主要策略文件添加详细中文注释，包括BBMod.py、BinHV27F.py、ClucHAnix.py等
  - **2025-07-24 文档化**：完成策略文件中文注释标准化工作
    - 建立策略文档化规范和标准
    - 完成BBMod.py、BinHV27F.py、ClucHAnix.py三个主要策略的详细中文注释
    - 为所有技术指标、买入卖出条件、自定义函数添加完整的中文说明
    - 提高代码可读性和维护性，便于后续开发和优化
  - **2025-07-24 杠杆配置**：为所有策略添加从config.json读取杠杆的支持
    - 新增策略杠杆支持：BBMod.py、E0V1EN.py、E0V1E.py、HourBasedStrategy.py
    - 修改现有策略杠杆逻辑：优先从config.json读取，如无配置则使用策略内部超参数
    - 涉及策略：ClucHAnix.py、BinHV27F.py、CombinedBinHAndCluc.py、ConsensusShort.py、BinHV27_short.py、ReinforcedQuickie.py
    - 特殊处理：E0V1E_Enhanced.py保留动态杠杆逻辑，但基础杠杆从config.json读取
    - 统一杠杆范围检查：确保杠杆在1.0到max_leverage之间
  - **2025-07-24 文档完善**：完成HourBasedStrategy.py和ReinforcedQuickie.py的详细中文注释
    - HourBasedStrategy.py：基于时间窗口的交易策略，包含完整的策略说明和参数注释
    - ReinforcedQuickie.py：强化快速交易策略，包含所有自定义技术指标和复杂逻辑的详细说明
    - 自定义技术指标文档化：RMI、ZEMA、SSL通道、PCC、SROC等指标的完整中文说明
    - 策略核心逻辑注释：趋势强化、多指标确认、自定义止损和退出逻辑的详细解释
    - 提升代码可读性：所有函数、参数、条件判断都有详细的中文注释
    - 代码格式优化：统一注释格式和代码风格，确保一致性
  - **2025-07-24 新策略开发**：创建EMAConvergenceStrategy.py EMA收敛交易策略
    - 基于EMA20和EMA200的比例关系和宽度收敛进行双向交易
    - 实现完整的超参数优化支持：比例阈值、收敛周期数
    - 使用简洁的入场信号语法：`["enter_long", "enter_tag"] = (1, "xxx")`
    - 修复trade.is_long属性问题，正确使用trade.is_short进行多空判断
    - 包含详细的中文注释和完整的策略文档
  - **2025-07-24 做空功能修复**：解决EMAConv.py策略没有做空交易的问题
    - 问题原因：策略缺少`can_short = True`设置，FreqTrade默认禁用做空功能
    - 解决方案：在策略类中添加`can_short = True`声明
    - 验证结果：回测显示573笔空头交易，5.76%收益率
    - 重要发现：FreqTrade的安全机制要求策略明确声明支持做空
    - 影响范围：所有需要做空功能的策略都必须添加此设置
  - **2025-07-25 EMA交叉退出修复**：解决EMAConv.py策略过早止损问题
    - 问题现象：大部分交易因8%止损提前退出，而不是等待EMA交叉点
    - 问题原因：`stoploss = -0.08`设置过于严格，导致在EMA交叉前被止损
    - 解决方案：将止损设置为`stoploss = -0.99`基本禁用止损，添加-50%极端亏损保护
    - 修复结果：64.1%的交易现在通过EMA交叉正确退出（50笔/78笔）
    - 退出统计：ema_cross_exit_short(41笔)、ema_cross_exit_long(9笔)、emergency_exit(18笔)、force_exit(10笔)
    - 重要经验：当策略依赖特定退出逻辑时，需要禁用或放宽默认止损设置
  - **2025-07-28 BollingerCross角度计算功能**：为布林带交叉策略添加角度过滤功能
    - 新增功能：计算布林带轨道斜率角度和穿越角度
    - 技术实现：使用线性回归和反正切函数计算角度
    - 超参数优化：添加最小角度阈值和计算周期参数
    - 信号过滤：只保留角度足够大的强烈穿越信号
    - 代码规范：明确禁止创建测试脚本，直接使用FreqTrade回测验证
  - **2025-07-28 DataFrame变量命名规范**：统一DataFrame变量使用短名称df
    - 编码规范：所有策略中的dataframe参数和变量统一使用df
    - 适用范围：包括函数参数、注释和文档字符串
    - 修改内容：更新BollingerCross.py策略中的注释，将dataframe改为df
    - 指南更新：在编码规范中添加DataFrame变量命名标准
    - 目的：提高代码一致性和可读性
  - **2025-07-28 BollingerCross超参数优化开发**：为布林带变化率条件添加超参数优化
    - 新增功能：将硬编码的布林带变化率阈值改为可优化参数
    - 做多条件：添加`buy_bbu_change_threshold`参数（0.5%-5%，默认1%）
    - 做空条件：添加`buy_bbl_change_threshold`参数（0.5%-5%，默认2%）
    - 参数空间规范：所有入场条件参数统一使用`space="buy"`
    - 重要发现：FreqTrade规范要求入场参数使用buy空间，退出参数使用sell空间
    - 应用效果：通过超参数优化可以分别找到做多和做空的最佳变化率阈值
  - **2025-07-30 Bollinger_1mx5m策略修复**：解决KeyError: '[nan] not in index'错误
    - 问题原因：populate_entry_trend中的enter_long和enter_short列未初始化，包含NaN值
    - 错误现象：在populate_exit_trend中使用df.loc[(df["enter_short"])]时出现pandas索引错误
    - 解决方案：在populate_entry_trend和populate_exit_trend开始时初始化所有信号列
    - 修复内容：添加df["enter_long"] = 0、df["enter_short"] = 0、df["enter_tag"] = ""等初始化
    - 重要规范：FreqTrade策略必须正确初始化所有信号列，避免NaN值导致的索引错误
    - 回测结果：修复后策略成功运行，11318笔交易，16.2%胜率，但整体亏损99.84%
    - 策略分析：信号反转退出逻辑有效（bb_expand和bb_break的long_signal_exit和short_signal_exit胜率70%+）
  - **2025-07-31 下载脚本自动更新START_DATE功能**：为download.sh添加数据下载成功后自动更新START_DATE的功能
    - 新增功能：下载数据成功后自动将.env文件中的START_DATE更新为30天前
    - 技术实现：使用`date -d "30 days ago"`（Linux）或`date -v-30d`（macOS）计算30天前日期
    - 脚本优化：修复shellcheck警告，直接检查命令退出状态而不是使用$?
    - 用户体验：添加成功提示信息，显示更新后的START_DATE值
    - 错误处理：只有在数据下载成功时才更新START_DATE，失败时保持原值
    - 兼容性：保持原有的END_DATE更新逻辑不变
    - 应用场景：适用于需要滚动更新数据窗口的自动化数据管理
  - **2025-08-03 ema.py策略变量名优化**：对布林带变化率交易策略进行变量命名优化
    - 策略重命名：将类名从`ema`改为`BollingerBandBreakoutStrategy`，使名称与实际功能匹配
    - 指标变量优化：
      * `bbl_change` → `bb_lower_change_rate`（下轨变化率）
      * `bbm_change` → `bb_middle_change_rate`（中轨变化率）
      * `bbu_change` → `bb_upper_change_rate`（上轨变化率）
      * `bbl_breakout` → `bb_lower_breakout`（下轨突破）
      * `bbu_breakout` → `bb_upper_breakout`（上轨突破）
    - SMA相关变量优化：
      * `bbl_sma` → `bb_lower_sma`（下轨SMA）
      * `bbm_sma` → `bb_middle_sma`（中轨SMA）
      * `bbu_sma` → `bb_upper_sma`（上轨SMA）
      * `bbw_sma` → `bb_width_sma`（宽度SMA）
      * `bbw_sma_ratio` → `bb_width_expansion_ratio`（宽度扩张比率）
    - 参数名称优化：
      * `buy_bbw_sma_window` → `buy_bb_width_sma_window`
      * `buy_bbw_expand_threshold` → `buy_bb_width_expansion_threshold`
    - 入场标签优化：
      * `price_breakout_long` → `bb_upper_breakout_long`
      * `price_breakout_short` → `bb_lower_breakout_short`
    - 代码结构改进：
      * 清理注释掉的无用代码
      * 提取条件判断变量，提高代码可读性
      * 改进中文注释，使逻辑更清晰
      * 统一变量命名规范，使用语义化命名
    - 优化效果：代码更易读、易维护，变量名与实际功能完全匹配

---
*此文件由AI自动读取，请保持更新以确保开发规范的一致性*
