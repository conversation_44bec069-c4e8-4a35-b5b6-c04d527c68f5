#!/usr/bin/env python3
"""
标记价格数据分析脚本

分析所有-8h-mark.feather文件，读取自2025年4月1日起的收盘价数据，
生成包含价格统计信息的CSV报告。

功能：
1. 读取所有-8h-mark.feather文件
2. 过滤2025年4月1日起的数据
3. 计算价格统计信息
4. 生成CSV输出文件

输出字段：
- symbol: 交易对符号
- start_price: 2025年4月1日的价格
- end_price: 最后的价格
- price_change_pct: 跌涨比例
- min_price: 最低价
- min_price_time: 最低价的时间
- max_price: 最高价
- max_price_time: 最高价的时间
- min_max_duration_hours: 最低价与最高价之间用时（小时）
- min_max_change_pct: 最低价与最高价的跌涨比例
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timezone
import warnings
import os

# 过滤pandas警告
warnings.filterwarnings("ignore", category=FutureWarning)

def find_mark_files(data_dir="user_data/data", exchange="binance"):
    """
    查找所有-8h-mark.feather文件
    
    Args:
        data_dir: 数据目录路径
        exchange: 交易所名称
        
    Returns:
        list: feather文件路径列表
    """
    futures_dir = Path(data_dir) / exchange / "futures"
    
    if not futures_dir.exists():
        print(f"数据目录不存在: {futures_dir}")
        return []
    
    # 查找所有8小时标记价格文件
    mark_files = list(futures_dir.glob("*-8h-mark.feather"))
    
    print(f"找到 {len(mark_files)} 个标记价格文件")
    return mark_files

def extract_symbol_from_filename(file_path):
    """
    从文件名提取交易对符号
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: 交易对符号
    """
    filename = Path(file_path).name
    # 移除-8h-mark.feather后缀
    symbol = filename.replace("-8h-mark.feather", "")
    return symbol

def analyze_price_data(file_path, start_date="2025-04-01"):
    """
    分析单个标记价格文件的数据
    
    Args:
        file_path: feather文件路径
        start_date: 开始日期（YYYY-MM-DD格式）
        
    Returns:
        dict: 价格统计信息
    """
    try:
        # 读取feather文件
        df = pd.read_feather(file_path)
        
        # 检查必要的列是否存在
        required_cols = ['date', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"文件 {file_path} 缺少必要列: {missing_cols}")
            return None
        
        # 确保date列是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(df['date']):
            df['date'] = pd.to_datetime(df['date'])
        
        # 过滤2025年4月1日起的数据
        start_datetime = pd.to_datetime(start_date)
        df_filtered = df[df['date'] >= start_datetime].copy()
        
        if len(df_filtered) == 0:
            print(f"文件 {file_path} 在 {start_date} 之后没有数据")
            return None
        
        # 按时间排序
        df_filtered = df_filtered.sort_values('date')
        
        # 提取交易对符号
        symbol = extract_symbol_from_filename(file_path)
        
        # 计算统计信息
        start_price = df_filtered['close'].iloc[0]
        end_price = df_filtered['close'].iloc[-1]
        min_price = df_filtered['close'].min()
        max_price = df_filtered['close'].max()
        
        # 找到最低价和最高价的时间
        min_price_idx = df_filtered['close'].idxmin()
        max_price_idx = df_filtered['close'].idxmax()
        min_price_time = df_filtered.loc[min_price_idx, 'date']
        max_price_time = df_filtered.loc[max_price_idx, 'date']
        
        # 计算跌涨比例
        price_change_pct = ((end_price - start_price) / start_price) * 100
        min_max_change_pct = ((max_price - min_price) / min_price) * 100
        
        # 计算最低价与最高价之间的时间差（小时）
        time_diff = abs(max_price_time - min_price_time)
        min_max_duration_hours = time_diff.total_seconds() / 3600
        
        return {
            'symbol': symbol,
            'start_price': round(start_price, 8),
            'end_price': round(end_price, 8),
            'price_change_pct': round(price_change_pct, 2),
            'min_price': round(min_price, 8),
            'min_price_time': min_price_time.strftime('%Y-%m-%d %H:%M:%S'),
            'max_price': round(max_price, 8),
            'max_price_time': max_price_time.strftime('%Y-%m-%d %H:%M:%S'),
            'min_max_duration_hours': round(min_max_duration_hours, 2),
            'min_max_change_pct': round(min_max_change_pct, 2)
        }
        
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
        return None

def main():
    """
    主函数：分析所有标记价格文件并生成CSV报告
    """
    print("开始分析标记价格数据...")
    
    # 查找所有标记价格文件
    mark_files = find_mark_files()
    
    if not mark_files:
        print("未找到任何标记价格文件，请先下载数据")
        return
    
    # 分析每个文件
    results = []
    for file_path in mark_files:
        print(f"正在分析: {file_path.name}")
        result = analyze_price_data(file_path)
        if result:
            results.append(result)
    
    if not results:
        print("没有有效的分析结果")
        return
    
    # 创建DataFrame并保存为CSV
    df_results = pd.DataFrame(results)
    
    # 按交易对符号排序
    df_results = df_results.sort_values('symbol')
    
    # 生成输出文件名（包含时间戳）
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"mark_price_analysis_{timestamp}.csv"
    
    # 保存CSV文件
    df_results.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n分析完成！")
    print(f"共分析了 {len(results)} 个交易对")
    print(f"结果已保存到: {output_file}")
    
    # 显示统计摘要
    print(f"\n统计摘要:")
    print(f"平均价格变化: {df_results['price_change_pct'].mean():.2f}%")
    print(f"最大涨幅: {df_results['price_change_pct'].max():.2f}% ({df_results.loc[df_results['price_change_pct'].idxmax(), 'symbol']})")
    print(f"最大跌幅: {df_results['price_change_pct'].min():.2f}% ({df_results.loc[df_results['price_change_pct'].idxmin(), 'symbol']})")
    print(f"平均最低最高价差: {df_results['min_max_change_pct'].mean():.2f}%")

if __name__ == "__main__":
    main()
