#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取加密货币交易所成交量最高的交易对（整合版）

功能：
1. 支持多个交易所（Binance、OKX）
2. 支持永续合约交易
3. 支持多种计价货币（USDT、BTC、ETH等）
4. 支持多种排序方式（市值、成交量）
5. 集成CoinGecko市值数据
6. 智能排除特定交易对
7. 支持多种输出格式
8. 币名修正功能
9. FreqTrade格式输出

使用方法：
python get_crypto_top_pairs.py                                    # 默认Binance永续合约按市值排序
python get_crypto_top_pairs.py --exchange okx                     # OKX永续合约
python get_crypto_top_pairs.py --sort-by volume -q BTC            # Binance BTC计价按成交量排序
python get_crypto_top_pairs.py -e okx -n 50                       # OKX前50个永续合约

依赖：
- requests (API调用和市值数据)
"""

import argparse
import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, List

import requests

# 所有功能都使用requests库，无需额外依赖


class CryptoExchangeAnalyzer:
    """加密货币交易所分析器（整合版）"""

    def __init__(
        self,
        exchange: str = "binance",
        quiet_mode: bool = False,
        quote_currency: str = "USDT",
        sort_by: str = "market_cap",
    ):
        """初始化分析器"""
        self.exchange = exchange.lower()
        self.quiet_mode = quiet_mode
        self.quote_currency = quote_currency.upper()
        self.sort_by = sort_by

        # 代理设置
        self.proxy_url = os.getenv("HTTPS_PROXY", "http://localhost:3213")
        self.proxies = {"http": self.proxy_url, "https": self.proxy_url} if self.proxy_url else None

        if not self.quiet_mode:
            exchange_name = "币安" if self.exchange == "binance" else "OKX"
            sort_desc = "市值" if self.sort_by == "market_cap" else "成交量"
            print(f"✓ 初始化完成 - 交易所: {exchange_name}, 类型: 永续合约, 排序: {sort_desc}")

    def _get_display_width(self, text: str) -> int:
        """计算字符串的实际显示宽度"""
        width = 0
        for char in text:
            if "\u4e00" <= char <= "\u9fff":
                width += 2
            else:
                width += 1
        return width

    def _pad_string(self, text: str, target_width: int, align: str = "left") -> str:
        """根据实际显示宽度填充字符串"""
        current_width = self._get_display_width(text)
        padding_needed = target_width - current_width

        if padding_needed <= 0:
            return text

        if align == "left":
            return text + " " * padding_needed
        elif align == "right":
            return " " * padding_needed + text
        else:  # center
            left_padding = padding_needed // 2
            right_padding = padding_needed - left_padding
            return " " * left_padding + text + " " * right_padding

    def get_market_cap_data(self) -> Dict[str, Dict[str, Any]]:
        """从CoinGecko API获取市值数据"""
        try:
            if not self.quiet_mode:
                print("📊 正在获取市值数据...")

            url = "https://api.coingecko.com/api/v3/coins/markets"
            params = {"vs_currency": "usd", "order": "market_cap_desc", "per_page": 500, "page": 1, "sparkline": False}

            response = requests.get(url, params=params, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            coins_markets = response.json()

            market_cap_data = {}
            for coin in coins_markets:
                symbol = coin["symbol"].upper()
                market_cap_data[symbol] = {
                    "market_cap": coin.get("market_cap", 0),
                    "market_cap_rank": coin.get("market_cap_rank", 999999),
                    "current_price": coin.get("current_price", 0),
                    "price_change_percentage_24h": coin.get("price_change_percentage_24h", 0),
                }

            if not self.quiet_mode:
                print(f"✓ 成功获取 {len(market_cap_data)} 个币种的市值数据")

            return market_cap_data

        except Exception as e:
            if not self.quiet_mode:
                print(f"⚠️ 获取市值数据失败: {e}")
                print("将回退到成交量排序")
            self.sort_by = "volume"
            return {}

    def get_binance_data(self) -> List[Dict[str, Any]]:
        """获取Binance永续合约数据"""
        try:
            if not self.quiet_mode:
                print("📊 正在获取Binance数据...")

            # 获取永续合约数据
            url = "https://fapi.binance.com/fapi/v1/ticker/24hr"
            response = requests.get(url, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            tickers = response.json()

            # 转换数据格式以匹配统一格式
            formatted_tickers = []
            for ticker in tickers:
                formatted_ticker = {
                    "symbol": ticker["symbol"],
                    "lastPrice": ticker["lastPrice"],
                    "volume": ticker["volume"],
                    "quoteVolume": ticker["quoteVolume"],
                    "priceChange": ticker["priceChange"],
                    "priceChangePercent": ticker["priceChangePercent"],
                    "highPrice": ticker["highPrice"],
                    "lowPrice": ticker["lowPrice"],
                    "count": ticker["count"],
                }
                formatted_tickers.append(formatted_ticker)

            if not self.quiet_mode:
                print(f"✓ 成功获取 {len(formatted_tickers)} 个交易对的数据")
            return formatted_tickers

        except Exception as e:
            print(f"❌ 获取Binance数据失败: {e}")
            sys.exit(1)

    def get_okx_data(self) -> List[Dict[str, Any]]:
        """获取OKX永续合约数据"""
        try:
            if not self.quiet_mode:
                print("📊 正在获取OKX数据...")

            url = "https://www.okx.com/api/v5/market/tickers?instType=SWAP"
            response = requests.get(url, timeout=10, proxies=self.proxies)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                tickers = data.get("data", [])
                if not self.quiet_mode:
                    print(f"✓ 成功获取 {len(tickers)} 个交易对的数据")
                return tickers
            else:
                raise Exception(f"OKX API错误: {data.get('msg', '未知错误')}")

        except Exception as e:
            print(f"❌ 获取OKX数据失败: {e}")
            sys.exit(1)

    def format_market_cap(self, market_cap: float) -> str:
        """格式化市值显示"""
        if not market_cap or market_cap == 0:
            return "N/A"

        if market_cap >= 1_000_000_000_000:
            return f"${market_cap / 1_000_000_000_000:.2f}T"
        elif market_cap >= 1_000_000_000:
            return f"${market_cap / 1_000_000_000:.2f}B"
        elif market_cap >= 1_000_000:
            return f"${market_cap / 1_000_000:.2f}M"
        elif market_cap >= 1_000:
            return f"${market_cap / 1_000:.2f}K"
        else:
            return f"${market_cap:.2f}"

    def format_volume(self, volume: float) -> str:
        """格式化交易量显示"""
        if volume >= 1_000_000_000:
            return f"{volume / 1_000_000_000:.2f}B"
        elif volume >= 1_000_000:
            return f"{volume / 1_000_000:.2f}M"
        elif volume >= 1_000:
            return f"{volume / 1_000:.2f}K"
        else:
            return f"{volume:.2f}"

    def run(self, top_n: int = 30, json_only: bool = False, config_format: bool = False):
        """运行主程序"""
        # 设置静默模式
        original_quiet_mode = self.quiet_mode
        if json_only or config_format:
            self.quiet_mode = True

        if not json_only and not config_format:
            exchange_name = "币安" if self.exchange == "binance" else "OKX"
            sort_desc = "市值" if self.sort_by == "market_cap" else "成交量"
            print(f"🚀 开始获取{exchange_name}永续合约按{sort_desc}排序的前{top_n}个{self.quote_currency}交易对...")
            print("-" * 80)

        # 1. 获取市值数据
        market_cap_data = {}
        if self.sort_by == "market_cap":
            market_cap_data = self.get_market_cap_data()

        # 2. 获取交易所数据
        if self.exchange == "binance":
            raw_data = self.get_binance_data()
        else:  # okx
            raw_data = self.get_okx_data()

        # 3. 处理和排序数据
        processed_data = self.process_data(raw_data, market_cap_data, top_n)

        # 4. 输出结果
        if config_format:
            config_pairs = self.format_config_output(processed_data)
            print('[\n  "' + '",\n  "'.join(config_pairs) + '"\n]')
        elif json_only:
            config_pairs = self.format_config_output(processed_data)
            print(json.dumps(config_pairs, ensure_ascii=False))
        else:
            self.print_results(processed_data, market_cap_data)
            # 输出JSON格式列表
            config_pairs = self.format_config_output(processed_data)
            config_json = '["' + '","'.join(config_pairs) + '"]'
            print(f"\n📋 JSON格式交易对列表 (前{len(processed_data)}个):")
            print("-" * 50)
            print(config_json)
            print("-" * 50)

        if not json_only and not config_format:
            print(f"\n✅ 任务完成！获取到 {len(processed_data)} 个交易对")

        return processed_data

    def process_data(self, raw_data: List[Dict], market_cap_data: Dict, top_n: int) -> List[Dict]:
        """处理和排序数据"""
        # 过滤指定计价货币的交易对
        filtered_data = self.filter_quote_pairs(raw_data)

        # 添加市值信息
        enriched_data = self.enrich_with_market_cap(filtered_data, market_cap_data)

        # 排序
        sorted_data = self.sort_pairs(enriched_data, top_n)

        return sorted_data

    def filter_quote_pairs(self, raw_data: List[Dict]) -> List[Dict]:
        """过滤指定计价货币的交易对"""
        filtered_pairs = []
        excluded_prefixes = self._get_excluded_prefixes()

        for ticker in raw_data:
            if self.exchange == "binance":
                # Binance永续合约格式：ETHUSDT
                symbol = ticker["symbol"]
                if symbol.endswith(self.quote_currency):
                    base_symbol = symbol[: -len(self.quote_currency)]
                    should_exclude = any(base_symbol.startswith(prefix) for prefix in excluded_prefixes)
                    if not should_exclude and float(ticker.get("quoteVolume", 0)) > 0:
                        filtered_pairs.append(ticker)
            else:  # okx
                # OKX永续合约格式：ETH-USDT-SWAP
                inst_id = ticker["instId"]
                if f"-{self.quote_currency}-SWAP" in inst_id:
                    base_symbol = inst_id.replace(f"-{self.quote_currency}-SWAP", "")
                    should_exclude = any(base_symbol.startswith(prefix) for prefix in excluded_prefixes)
                    if not should_exclude and float(ticker.get("volCcy24h", 0)) > 0:
                        filtered_pairs.append(ticker)

        if not self.quiet_mode:
            print(f"✓ 筛选出 {len(filtered_pairs)} 个活跃的{self.quote_currency}交易对")
        return filtered_pairs

    def _get_excluded_prefixes(self) -> List[str]:
        """获取需要排除的交易对前缀"""
        base_excluded = ["BTC", "BNB"]

        if self.quote_currency == "USDT":
            base_excluded.extend(["USDC", "FDUS", "WBTC"])
        elif self.quote_currency == "BTC":
            base_excluded.extend(["USDT", "USDC", "BUSD"])
        elif self.quote_currency == "ETH":
            base_excluded.extend(["USDT", "USDC", "BUSD"])

        return base_excluded

    def enrich_with_market_cap(self, filtered_data: List[Dict], market_cap_data: Dict) -> List[Dict]:
        """为数据添加市值信息"""
        enriched_data = []

        for ticker in filtered_data:
            if self.exchange == "binance":
                symbol = ticker["symbol"]
                base_symbol = symbol[: -len(self.quote_currency)]
            else:  # okx
                inst_id = ticker["instId"]
                base_symbol = inst_id.replace(f"-{self.quote_currency}-SWAP", "")

            # 添加市值信息
            market_info = market_cap_data.get(base_symbol, {})
            ticker["base_symbol"] = base_symbol
            ticker["market_cap"] = market_info.get("market_cap", 0)
            ticker["market_cap_rank"] = market_info.get("market_cap_rank", 999999)

            enriched_data.append(ticker)

        return enriched_data

    def sort_pairs(self, enriched_data: List[Dict], top_n: int) -> List[Dict]:
        """排序交易对"""
        if self.sort_by == "market_cap":
            # 按市值排名排序（排名越小越靠前）
            sorted_data = sorted(enriched_data, key=lambda x: x.get("market_cap_rank", 999999))
        else:
            # 按成交量排序
            if self.exchange == "binance":
                sorted_data = sorted(enriched_data, key=lambda x: float(x.get("quoteVolume", 0)), reverse=True)
            else:  # okx
                sorted_data = sorted(enriched_data, key=lambda x: float(x.get("volCcy24h", 0)), reverse=True)

        return sorted_data[:top_n]

    def format_config_output(self, processed_data: List[Dict]) -> List[str]:
        """格式化为FreqTrade永续合约config格式"""
        config_pairs = []
        for ticker in processed_data:
            base_symbol = ticker["base_symbol"]
            config_pair = f"{base_symbol}/{self.quote_currency}:{self.quote_currency}"
            config_pairs.append(config_pair)
        return config_pairs

    def print_results(self, processed_data: List[Dict], market_cap_data: Dict):
        """打印结果表格"""
        exchange_name = "币安" if self.exchange == "binance" else "OKX"

        # 计算列宽
        rank_width = 4
        symbol_width = 16
        volume_width = 20
        market_cap_width = 15
        price_width = 15
        change_width = 12
        total_width = rank_width + symbol_width + volume_width + market_cap_width + price_width + change_width + 5

        print("\n" + "=" * total_width)
        print(f"🏆 {exchange_name}永续合约24h内成交量最高的{len(processed_data)}个{self.quote_currency}交易对")
        print("=" * total_width)

        # 打印标题
        headers = ["排名", "交易对", f"24h交易量({self.quote_currency})", "市值", "最新价格", "24h涨跌幅"]
        print(
            self._pad_string(headers[0], rank_width)
            + " "
            + self._pad_string(headers[1], symbol_width)
            + " "
            + self._pad_string(headers[2], volume_width)
            + " "
            + self._pad_string(headers[3], market_cap_width)
            + " "
            + self._pad_string(headers[4], price_width)
            + " "
            + self._pad_string(headers[5], change_width)
        )
        print("-" * total_width)

        # 打印数据行
        for idx, ticker in enumerate(processed_data, 1):
            base_symbol = ticker["base_symbol"]

            if self.exchange == "binance":
                volume = self.format_volume(float(ticker.get("quoteVolume", 0)))
                price = f"{float(ticker.get('lastPrice', 0)):.6f}"
                change = f"{float(ticker.get('priceChangePercent', 0)):+.2f}%"
            else:  # okx
                volume = self.format_volume(float(ticker.get("volCcy24h", 0)))
                price = f"{float(ticker.get('last', 0)):.6f}"
                try:
                    open_24h = float(ticker.get("open24h", 0))
                    last_price = float(ticker.get("last", 0))
                    if open_24h > 0:
                        change_pct = ((last_price - open_24h) / open_24h) * 100
                        change = f"{change_pct:+.2f}%"
                    else:
                        change = "N/A"
                except (ValueError, TypeError, ZeroDivisionError):
                    change = "N/A"

            market_cap = self.format_market_cap(ticker.get("market_cap", 0))
            # 表格显示时只显示基础币种，不显示 /USDT:USDT 后缀
            display_symbol = base_symbol

            print(
                self._pad_string(str(idx), rank_width)
                + " "
                + self._pad_string(display_symbol, symbol_width)
                + " "
                + self._pad_string(volume, volume_width)
                + " "
                + self._pad_string(market_cap, market_cap_width)
                + " "
                + self._pad_string(price, price_width)
                + " "
                + self._pad_string(change, change_width)
            )

        print("-" * total_width)
        print(f"📊 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        excluded_prefixes = self._get_excluded_prefixes()
        excluded_str = "、".join(excluded_prefixes)
        print(f"🚫 已排除{excluded_str}相关交易对")
        if self.sort_by == "market_cap":
            print("📈 按市值排序（数据来源：CoinGecko）")
        else:
            print("📊 按成交量排序")
        print("=" * total_width)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="获取加密货币交易所永续合约排行榜",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                                    # 默认Binance永续合约按市值排序
  %(prog)s -e okx                            # OKX永续合约
  %(prog)s --sort-by volume -q BTC           # Binance BTC计价按成交量排序
  %(prog)s -e okx -n 50                      # OKX前50个永续合约

特性:
  ✓ 支持多个交易所（Binance、OKX）
  ✓ 专注永续合约交易
  ✓ 支持按市值或成交量排序
  ✓ 集成CoinGecko市值数据
  ✓ FreqTrade格式输出
        """,
    )

    parser.add_argument("-e", "--exchange", type=str, default="binance", choices=["binance", "okx"], help="交易所选择 (默认: binance)")
    parser.add_argument("-n", "--number", type=int, default=50, help="获取前N个交易对 (默认: 50)")
    parser.add_argument("-q", "--quote-currency", type=str, default="USDT", help="计价货币 (默认: USDT)")
    parser.add_argument("--sort-by", type=str, default="market_cap", choices=["market_cap", "volume"], help="排序方式 (默认: market_cap)")
    parser.add_argument("--json-only", action="store_true", help="仅输出JSON格式")
    parser.add_argument("--config-format", action="store_true", help="输出config.json格式")

    args = parser.parse_args()

    try:
        analyzer = CryptoExchangeAnalyzer(
            exchange=args.exchange,
            quiet_mode=args.config_format or args.json_only,
            quote_currency=args.quote_currency,
            sort_by=args.sort_by,
        )
        results = analyzer.run(
            top_n=args.number,
            json_only=args.json_only,
            config_format=args.config_format,
        )
        return results
    except KeyboardInterrupt:
        if not args.json_only:
            print("\n\n⚠️  用户中断程序")
        sys.exit(0)
    except Exception as e:
        if not args.json_only:
            print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
